{"name": "My workflow 3", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "441a947b-831a-4790-bdc9-a63cc48afb2d", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-2512, 96], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "b363d9b7-1078-4f21-a6cf-700a4411c471", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-2368, 96]}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet lastName = (message && message.from && message.from.last_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.last_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.last_name);\n\nlet isCallback = !!callbackQuery;\nlet isNewChatMember = !!chatMember;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    unifiedLastName: lastName,\n    isCallback: isCallback,\n    isNewChatMember: isNewChatMember\n  }\n};"}, "id": "b866ac5e-9ef7-4fc1-98cb-f7d5b29a140b", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2064, 112]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}], "combineOperation": "any"}, "options": {}}, "id": "205c2db2-c927-4a44-9cdb-42eb965e218a", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2528, 320]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ typeof $json.unifiedUserId }}", "rightValue": "undefined", "operation": {"type": "string", "operation": "notEqual", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "8853404e-8cd1-4b7d-86fa-d747d9a7b74d", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2352, 304]}, {"parameters": {"jsCode": "// Debug: Check what data is being passed to user lookup\nconsole.log('Data before user lookup:', JSON.stringify($json, null, 2));\nconsole.log('Unified User ID:', $json.unifiedUserId);\nconsole.log('Type of User ID:', typeof $json.unifiedUserId);\n\nreturn $input.all();"}, "id": "41242a6a-f035-43a9-a539-1ebc108ae28f", "name": "Debug Before User Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1664, 528]}, {"parameters": {"jsCode": "// Test Supabase connection by checking if we can reach this point\nconsole.log('=== ABOUT TO QUERY SUPABASE ===');\nconsole.log('User ID to search:', $json.unifiedUserId);\nconsole.log('User ID type:', typeof $json.unifiedUserId);\nconsole.log('Full data object:', JSON.stringify($json, null, 2));\nreturn $input.all();"}, "id": "beb0312a-f9bb-483f-b227-038e0b6f7faa", "name": "Test Supabase Connection", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2208, 80]}, {"parameters": {"operation": "getAll", "tableId": "users", "returnAll": true, "filters": {"conditions": [{"keyName": "telegram_id", "condition": "eq", "keyValue": "={{ $json.unifiedUserId.toString() }}"}]}}, "id": "6bb7ae15-ac4b-420f-aaeb-ba0db416b0f3", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2016, 304], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Merge Supabase results with original user data\nconst originalData = $input.first().json;\nconst supabaseResults = $input.last().json;\n\nconsole.log('=== MERGING USER DATA ===');\nconsole.log('Original data:', JSON.stringify(originalData, null, 2));\nconsole.log('Supabase results:', JSON.stringify(supabaseResults, null, 2));\n\n// Create merged result\nconst mergedData = {\n  ...originalData,\n  supabaseResults: Array.isArray(supabaseResults) ? supabaseResults : [supabaseResults],\n  length: Array.isArray(supabaseResults) ? supabaseResults.length : (supabaseResults ? 1 : 0),\n  userFound: Array.isArray(supabaseResults) ? supabaseResults.length > 0 : !!supabaseResults\n};\n\nconsole.log('Merged result:', JSON.stringify(mergedData, null, 2));\nconsole.log('=== END MERGE ===');\n\nreturn { json: mergedData };"}, "id": "133338fc-d5cf-4fd4-a0b6-9c239181f3bd", "name": "Merge User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2176, 512]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}, "id": "50af86e1-d69e-4842-9ffc-8e517dfa27bc"}], "combineOperation": "and"}, "options": {"looseTypeValidation": false}}, "id": "fe7a3848-0692-4be7-8f0d-aeb8caca9345", "name": "User Exists Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1712, 320], "alwaysOutputData": false}, {"parameters": {"tableId": "users", "dataToSend": "autoMapInputData"}, "id": "4de7bf46-711b-4b0b-af62-54244fe99622", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1536, 368], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\n// Debug: Log the user creation result\nconsole.log('New user created:', JSON.stringify($json, null, 2));\n\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true,\n    debug_user_creation: true\n  }\n};"}, "id": "fee6ef2e-54f9-4ced-af4b-b935c7b269f4", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1344, 336]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\n// Get original user data from Standardize User Data node\nconst originalData = $('Standardize User Data').item.json;\nconst supabaseUser = $json[0] || $json;\n\nconsole.log('Existing user found:', JSON.stringify(supabaseUser, null, 2));\nconsole.log('Original data:', JSON.stringify(originalData, null, 2));\n\nreturn {\n  json: {\n    ...originalData,\n    user: supabaseUser,\n    is_new_user: false,\n    debug_existing_user: true\n  }\n};"}, "id": "ff72aecf-029c-41d1-8c35-66efcfc2b2ba", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1856, 176]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "main"}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}]}}, "id": "c70fb70c-bbde-4575-85c3-f9c834664f2c", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1264, 704], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "const user = $json.user;\nconst isAdmin = user?.role === 'admin';\n\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\nconst staticKeyboard = [\n  [\n    { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n    { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n  ],\n  [\n    { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n    { text: \"💝 Donate\", callback_data: \"donate\" }\n  ],\n  [\n    { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n    { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n  ]\n];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: staticKeyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "64f47cb1-17ac-48bd-b4fd-79045e9169b1", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-832, 240]}, {"parameters": {"chatId": "={{ $json.chatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "088e8403-f626-4d54-994c-81caec6f4c7e", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-576, 240], "webhookId": "f7808a34-8de2-4cca-9313-c1ec353740df", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "4f14ca97-033e-4535-a97b-648ff57052e7", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-816, 432], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "c493366b-c2b0-4df9-bc68-ad674c6263be", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-592, 448]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "1128ac90-2d2a-410c-b6ad-0393ce33e974", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-336, 352], "webhookId": "cf66780a-dbcf-441d-aa1b-e62ffe47170e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "dfcf5234-3ed7-4dc0-891b-dd35d6439223", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1104, 336], "webhookId": "04b66bdf-bcbc-4144-b12f-6b25d86e4b0c", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "id": "7a647b8f-f616-4e80-a058-874a39e3069d", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-912, 672], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true", "rightType": "any"}}, {"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "323298a1-6ae0-411b-bf1f-d2edcf29923e", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-208, 960]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Hello {{ $json[0]?.name || 'Investor' }}! Your current investment value is: ${{ $json[0]?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "339f60c9-25e8-48eb-8d2b-0f800d588dce", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [16, 912], "webhookId": "3fd54c83-3909-4de6-a53c-34c37f734e3b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "a218cc7d-4ae0-4a57-ada8-df671bc7c2f4", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [256, 1040], "webhookId": "e797834f-b962-4a70-ad7c-7d45762ad3aa", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst serviceButtons = [\n  [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n  [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n  [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n  [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n];\n\nconst keyboard = [...serviceButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "a2c50acb-bd48-4e82-be5c-b4d3d649f107", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-896, 1088]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "1aa08135-2758-4986-b1a1-7580acea115c", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 1088], "webhookId": "ce4c0427-72a9-451e-aa40-4b9dac6bcbf8", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst donateButtons = [\n  [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n  [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n  [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n  [{ text: \"Custom Amount\", callback_data: \"donate_custom\" }]\n];\n\nconst keyboard = [...donateButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "574f21b5-6633-41b3-9c90-1375eed13551", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-784, 1232]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "6e0cb31d-bd2b-402a-8554-1303b7df032a", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-592, 1232], "webhookId": "1cca6b01-d29b-4f44-95fc-73fa22064cac", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons.", "additionalFields": {}}, "id": "dac767ca-c606-4a7e-a8a9-e3e578cf3918", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1600, 992], "webhookId": "54a07ea0-e42b-49cd-8ad1-57700d90dc54", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}, {"keyName": "role"}]}}, "id": "d5059426-8cba-4b5b-838e-b2f1cf5e9a5b", "name": "Check if User is Admin1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-544, 768], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}, {"keyName": "role"}]}}, "id": "b2565199-2fbb-4b33-9b08-5ba73f6ca34a", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-704, 624], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{$node['Check if User is Admin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}, {"leftValue": "={{$node['Check if User is Admin'].json[0]?.role}}", "rightValue": "admin", "operation": {"type": "string", "operation": "equal", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "cebf8adb-0402-4588-aa67-cebe243124e8", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-528, 624]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "6d7550fb-b42a-42ea-baaa-f43cd072ab41", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-160, 560], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $node[\"Check User in Database\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true", "rightType": "any"}}, {"leftValue": "={{ $node[\"Check User in Database\"].json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "bbd49c36-d42e-4490-b4e2-c58641fcae21", "name": "Check if Verified Investor1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-368, 784]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"Check User in Database\"].json[0].name }}! Your current investment value is: ${{ $node[\"Check User in Database\"].json[0].investment_details.total_value_usd }}.", "additionalFields": {}}, "id": "b1d86d12-47d4-4482-8673-cb287e6dfe26", "name": "Send Investment Details1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-64, 720], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "❌ *Invalid User Data*\n\nSorry, we couldn't process your request due to invalid user information. This might happen if:\n\n• Your user ID is missing or corrupted\n• There's a temporary data issue\n• The bot needs to be restarted\n\n🔄 Please try using /start to restart the bot.\n📞 If the problem persists, contact support.", "additionalFields": {}}, "id": "bf1b49f7-9f24-4ac7-91b4-914240d83924", "name": "Invalid User E<PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2192, 848], "webhookId": "dcfd838e-0704-44c5-a6f5-3c50e6fac1bf", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "🔧 *Database Connection Error*\n\nWe're experiencing temporary technical difficulties with our database. Please try again in a few moments.\n\n⏳ This is usually resolved quickly\n🔄 Use /start to try again\n📞 Contact support if the issue persists", "additionalFields": {}}, "id": "edb703b7-d567-459e-a8ef-1b53e4ff88d7", "name": "Database Error <PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1520, 688], "webhookId": "7f250e6b-0761-43ec-b6a9-3aa81a7d9943", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Test Supabase Connection", "type": "main", "index": 0}]]}, "Standardize User Data": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "Event Router": {"main": [[{"node": "Validate User ID", "type": "main", "index": 0}]]}, "User Exists Check": {"main": [[{"node": "<PERSON> Existing User", "type": "main", "index": 0}], [{"node": "Create New User", "type": "main", "index": 0}]]}, "Create New User": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Database Error <PERSON>", "type": "main", "index": 0}]]}, "Mark New User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}]]}, "Send Welcome": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}]]}, "Build Main Menu": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}]]}, "Mark Existing User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Build Services Menu": {"main": [[{"node": "Send Services Menu", "type": "main", "index": 0}]]}, "Build Donate Menu": {"main": [[{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Check if User is Admin": {"main": [[{"node": "IF Admin", "type": "main", "index": 0}]]}, "IF Admin": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}], [{"node": "Check if Verified Investor1", "type": "main", "index": 0}]]}, "Check if Verified Investor1": {"main": [[{"node": "Send Investment Details1", "type": "main", "index": 0}], [{"node": "Check if Verified Investor", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if User is Admin", "type": "main", "index": 0}]]}, "Send Main Menu": {"main": [[{"node": "Check User in Database", "type": "main", "index": 0}]]}, "Check Existing User": {"main": [[{"node": "User Exists Check", "type": "main", "index": 0}]]}, "Test Supabase Connection": {"main": [[{"node": "Standardize User Data", "type": "main", "index": 0}]]}, "Validate User ID": {"main": [[{"node": "Merge User Data", "type": "main", "index": 0}]]}, "Merge User Data": {"main": [[{"node": "Check Existing User", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "7f82b413-d628-4345-93d7-9d5a5a614e22", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "cl97drsgNofoPdxU", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}