{"name": "My workflow copy 2", "nodes": [{"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "{{$json[\"message\"][\"text\"]}}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}, "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Welcome"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "62d8cb3b-43ab-4f99-80e4-6f1741b46d42", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/vendors", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Vend<PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-query-condition", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "Vend<PERSON>", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fa012ef9-5b99-4922-b134-fca073f90994", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/menu", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "27f37457-3e10-4eb8-a9cd-dbceef71f5ad", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/order", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Order"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-browse-menu", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "browse_menu", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-place-order", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "place_order", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackOrder"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-back-to-start", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "back_to_start", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackBackToStart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-back-to-vendors", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "back_to_vendors", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackBackToVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-vendor-selection", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "vendor_", "operator": {"type": "string", "operation": "startsWith"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-mains", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_mains", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "MainsMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-drinks", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_drinks", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DrinksMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-desserts", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_desserts", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DessertsMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-sides", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_sides", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "SidesMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "add_", "operator": {"type": "string", "operation": "startsWith"}, "id": "add-item-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "AddItemToCart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "menu_", "operator": {"type": "string", "operation": "startsWith"}, "id": "vendor-menu-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorMenuSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "order_", "operator": {"type": "string", "operation": "startsWith"}, "id": "vendor-order-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorOrderSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "pre_checkout_query", "operator": {"type": "string", "operation": "equals"}, "id": "pre-checkout-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PreCheckoutQuery"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "successful_payment", "operator": {"type": "string", "operation": "equals"}, "id": "successful-payment-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "SuccessfulPayment"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "view_cart", "operator": {"type": "string", "operation": "equals"}, "id": "view-cart-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ViewCart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "checkout", "operator": {"type": "string", "operation": "equals"}, "id": "checkout-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Checkout"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "clear_cart", "operator": {"type": "string", "operation": "equals"}, "id": "clear-cart-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ClearCart"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [0, 0], "id": "a61e764e-5264-4791-b88f-6ab2cc75cd56", "name": "Switch"}], "pinData": {}, "connections": {}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e3258d99-e7cd-4c5b-b085-18ed9caf46fa", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "7Cqv2KhmUIHqpaKr", "tags": []}