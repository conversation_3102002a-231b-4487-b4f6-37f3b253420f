{"name": "My workflow copy", "nodes": [{"parameters": {"updates": ["message", "callback_query", "pre_checkout_query", "successful_payment"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1056, 128], "id": "bad90427-8414-4599-be6b-f3d2e8d16a40", "name": "<PERSON>eg<PERSON>", "webhookId": "23b39a94-f325-4058-9e19-68f6cc044920", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "{{$json[\"message\"][\"text\"]}}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}, "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Welcome"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "62d8cb3b-43ab-4f99-80e4-6f1741b46d42", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/vendors", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Vend<PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-query-condition", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "Vend<PERSON>", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-browse-menu", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "browse_menu", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-back-to-start", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "back_to_start", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackBackToStart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-back-to-vendors", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "back_to_vendors", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackBackToVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-vendor-selection", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "vendor_", "operator": {"type": "string", "operation": "startsWith"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "add_", "operator": {"type": "string", "operation": "startsWith"}, "id": "add-item-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "AddItemToCart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "menu_", "operator": {"type": "string", "operation": "startsWith"}, "id": "vendor-menu-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorMenuSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "pre_checkout_query", "operator": {"type": "string", "operation": "equals"}, "id": "pre-checkout-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PreCheckoutQuery"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "successful_payment", "operator": {"type": "string", "operation": "equals"}, "id": "successful-payment-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "SuccessfulPayment"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "view_cart", "operator": {"type": "string", "operation": "equals"}, "id": "view-cart-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ViewCart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "checkout", "operator": {"type": "string", "operation": "equals"}, "id": "checkout-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Checkout"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "clear_cart", "operator": {"type": "string", "operation": "equals"}, "id": "clear-cart-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ClearCart"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-816, 128], "id": "adf8952f-9f3e-40c9-9caf-5832ff12498d", "name": "Switch"}, {"parameters": {"chatId": "={{ $json.message.from.id }}", "text": "Choose an option below to get started.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Vendors", "additionalFields": {"callback_data": "Vend<PERSON>"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-576, -128], "id": "2b68cb4f-bcdc-45a3-9e03-4886520cd019", "name": "Send Welcome Message", "webhookId": "25b4aff6-f359-4369-8902-067cd6be39ec", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "callback-exists-check", "leftValue": "={{ $json.callback_query }}", "rightValue": "", "operator": {"type": "object", "operation": "exists"}}], "combinator": "and"}, "options": {}}, "id": "f2bcc5d3-9f0c-4fc0-9ef5-7d5fdcbcd4e1", "name": "Is Callback Query?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1056, -96]}, {"parameters": {"resource": "callback", "queryId": "={{ $json.callback_query.id }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-816, -96], "id": "99181d0d-5411-4cd4-ab82-acfca40a5a32", "name": "Answer Callback Query", "webhookId": "f0dda62d-a686-4c54-9c42-e9c67caf5223", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"operation": "rpc"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-576, 96], "id": "1231b683-c591-4099-a29c-19db3995cadf", "name": "Get Vendors", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "let vendor_buttons = [];\nconst vendors = items[0].json.data;\n\n// Create a button for each vendor\nfor (const vendor of vendors) {\n  vendor_buttons.push({ \n    text: vendor.name, \n    callback_data: `vendor_${vendor.id}` \n  });\n}\n\n// n8n's Telegram node expects buttons to be in nested arrays (rows)\n// Let's put 2 buttons per row for a cleaner look\nconst rows = [];\nfor (let i = 0; i < vendor_buttons.length; i += 2) {\n  rows.push(vendor_buttons.slice(i, i + 2));\n}\n\n// Add a back button on its own row\nrows.push([{ text: '⬅️ Back to Start', callback_data: 'back_to_start' }]);\n\n$item.chatId = $json.message ? $json.message.from.id : $json.callback_query.from.id;\n$item.text = 'Please select a vendor from the list below:';\n$item.inline_keyboard = rows;\n\nreturn $item;"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-352, 96], "id": "5f295780-98c6-4ddb-ba7f-6ed8faccd566", "name": "Generate V<PERSON><PERSON>"}, {"parameters": {"chatId": "={{$item.chatId}}", "text": "={{$item.text}}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}, "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, 96], "id": "bd49ace5-18a1-4799-b3dc-ea414d223cca", "name": "Send Dynamic Vendor List", "webhookId": "eaeab0fc-de26-4518-bd32-28701179fcdb", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"operation": "rpc"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-576, 272], "id": "ee504b38-d83b-405e-a430-0bf4520ad23e", "name": "Get Vendor Details", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "const vendor = items[0].json.data[0];\n\n// Store vendor name for later use in checkout\nconst userId = $json.callback_query.from.id;\nglobal.set(`vendor_${userId}`, vendor.name);\n\nlet menuButtons = vendor.menus.map(menu => ({ \n  text: menu.name, \n  callback_data: `menu_${menu.id}` \n}));\n\nconst rows = [];\nfor (let i = 0; i < menuButtons.length; i += 2) {\n  rows.push(menuButtons.slice(i, i + 2));\n}\n\nrows.push([{ text: '⬅️ Back to Vendors', callback_data: 'back_to_vendors' }]);\n\nlet messageText = `*${vendor.name}*\\n\\n${vendor.description}\\n\\nSelect a menu category to continue:`;\n\n$item.text = messageText;\n$item.inline_keyboard = rows;\n\nreturn $item;"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-352, 272], "id": "cfd2f87f-7c55-4d30-9d5b-10c3ad92dc28", "name": "Generate <PERSON><PERSON>"}, {"parameters": {"chatId": "={{ $json.callback_query.from.id }}", "text": "={{$item.text}}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}, "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, 272], "id": "c3609d19-147b-4829-8ba9-b4f3146ea2f9", "name": "Send Dynamic Vendor Details", "webhookId": "21468f68-4abe-4b9d-b6d8-2639213135d7", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"operation": "rpc"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-576, 448], "id": "ebe59f3d-4b5b-4030-9196-21e856e5c994", "name": "Get Menu Items", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "const items = $item.json.data;\n\nlet messageText = 'Tap an item to add it to your cart:\\n';\n\nconst itemButtons = items.map(item => {\n  return { \n    text: `🛒 ${item.name} - $${item.price.toFixed(2)}`, \n    callback_data: `add_${item.id}`\n  };\n});\n\nconst rows = [];\n// Put each item on its own row\nfor (const button of itemButtons) {\n  rows.push([button]);\n}\n\n// Get vendor details to go back to the correct menu\nconst vendorDetailsNode = $nodes(\"Get Vendor Details\");\nconst vendorId = vendorDetailsNode[0].json.data[0].id;\n\nrows.push([{ text: '⬅️ Back to Menu Categories', callback_data: `vendor_${vendorId}` }]);\n\n$item.text = messageText;\n$item.inline_keyboard = rows;\n\nreturn $item;"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-352, 448], "id": "b82e9976-099a-4886-be32-7d0dcc306818", "name": "Format Menu Items"}, {"parameters": {"chatId": "={{ $json.callback_query.from.id }}", "text": "={{$item.text}}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}, "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, 448], "id": "4e08ee99-b4ae-4358-a8c9-3e2eb7625e87", "name": "Send Dynamic Menu Items", "webhookId": "631bc224-8a24-46dd-b758-2c400b5d9691", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const callbackData = $json.callback_query.data;\nconst itemId = callbackData.replace('add_', '');\n\nreturn [{ json: { itemId: itemId } }];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-576, 624], "id": "0a526ae8-d8d2-4c58-a876-95004ca6a961", "name": "Extract Item Info"}, {"parameters": {"operation": "get", "tableId": "menu_items"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-352, 624], "id": "a9020d7b-ba97-4bff-aa35-ba77b18ec862", "name": "Get Item Details", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "const item = items[0].json.data[0];\nconst userId = $json.callback_query.from.id;\n\nif (!item) {\n  return [{ json: { error: 'Item not found' } }];\n}\n\nlet userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\nlet existingItem = userCart.items.find(cartItem => cartItem.id === item.id);\n\nif (existingItem) {\n  existingItem.quantity += 1;\n} else {\n  userCart.items.push({\n    id: item.id,\n    name: item.name,\n    price: item.price,\n    quantity: 1\n  });\n}\n\nuserCart.total = userCart.items.reduce((sum, cartItem) => sum + (cartItem.price * cartItem.quantity), 0);\nglobal.set(`cart_${userId}`, userCart);\n\nreturn [{ json: { \n  message: `✅ *${item.name}* added to cart!`,\n  userId: userId\n} }];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-128, 624], "id": "bb18b689-1c3f-4d46-811e-7974167566a7", "name": "Add Item to Cart"}, {"parameters": {"chatId": "={{$json.userId}}", "text": "={{$json.message}}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [96, 624], "id": "4b58e869-663c-4d5c-ac52-0a5659f8a60c", "name": "Send 'Added to <PERSON><PERSON><PERSON> <PERSON><PERSON>", "webhookId": "b3fe619f-b4c8-43ec-bd05-ef6ad6452060", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}, "notes": "This node sends a small, temporary pop-up alert to the user in Telegram to confirm an item was added, without changing the menu screen."}, {"parameters": {"functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  $item.text = '🛒 Your cart is empty!';\n  $item.inline_keyboard = [[{ text: '⬅️ Back to Vendors', callback_data: 'back_to_vendors' }]];\n  return $item;\n}\n\nlet cartText = '🛒 *Your Shopping Cart*\\n\\n';\nuserCart.items.forEach(item => {\n  cartText += `*${item.name}* (x${item.quantity}) - $${(item.price * item.quantity).toFixed(2)}\\n`;\n});\ncartText += `\\n*Total: $${userCart.total.toFixed(2)}*`;\n\n$item.text = cartText;\n$item.inline_keyboard = [\n  [{ text: '💳 Proceed to Checkout', callback_data: 'checkout' }],\n  [{ text: '➕ Add More Items', callback_data: 'back_to_vendors' }, { text: '🗑️ Clear Cart', callback_data: 'clear_cart' }]\n];\n\nreturn $item;"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-576, 800], "id": "4107a983-bde4-4ba0-9bcb-605789fef604", "name": "View Cart Details"}, {"parameters": {"chatId": "={{ $json.callback_query.from.id }}", "text": "={{$item.text}}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}, "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-352, 800], "id": "d52a793f-b1d8-4f8b-9fdb-b33f5b941169", "name": "Send Cart Details", "webhookId": "e279ae56-4ec5-4e01-a962-b278f1df7518", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  return []; // Stop execution if cart is empty\n}\n\n// Create the 'prices' array for the invoice\n// Telegram requires the amount in the smallest currency unit (e.g., cents)\nconst prices = userCart.items.map(item => ({\n  label: `${item.name} x${item.quantity}`,\n  amount: Math.round(item.price * item.quantity * 100)\n}));\n\n// Prepare summary text for the invoice description\nlet invoiceDescription = \"Your order includes:\\n\";\nuserCart.items.forEach(item => {\n  invoiceDescription += `• ${item.name} x${item.quantity}\\n`;\n});\ninvoiceDescription += `\\nTotal: $${userCart.total.toFixed(2)}`;\n\nreturn [{\n  json: {\n    prices: prices,\n    userId: userId,\n    invoiceDescription: invoiceDescription,\n    payload: `checkout-${userId}-${Date.now()}`\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-576, 960], "id": "59b4e655-a604-4f98-9f3e-e090868fb1ce", "name": "Prepare Checkout"}, {"parameters": {"resource": "invoice"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-352, 960], "id": "18a756a6-482f-437e-8958-0de9f13859d9", "name": "Send Invoice", "webhookId": "29a7a55f-99e7-47f2-a6d7-a1c8f18f9ecf", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}, "notes": "IMPORTANT: Replace 'YOUR_TELEGRAM_PAYMENTS_PROVIDER_TOKEN' with your actual token from @BotFather."}, {"parameters": {"resource": "preCheck<PERSON><PERSON><PERSON><PERSON>"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-576, 1120], "id": "bcb4d740-2bcc-478b-a421-ce3cb2a5acc2", "name": "Answer Pre-Checkout Query", "webhookId": "ff70f238-5e70-45fa-aebb-3053252ff2e7", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.successful_payment.from.id;\n// Clear the user's cart after successful payment\nglobal.set(`cart_${userId}`, { items: [], total: 0 });\n\nconst confirmationMessage = \"✅ Thank you for your payment! Your order has been confirmed and is being prepared.\";\n\nreturn [{\n    json: {\n        chatId: userId,\n        message: confirmationMessage\n    }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-576, 1280], "id": "62d2d5c3-c85a-4801-937b-46aeb726baae", "name": "Finalize Order"}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-352, 1280], "id": "9d0eb450-3d69-4cc9-ba5d-1c2193213eb2", "name": "Send Payment Confirmation", "webhookId": "21bf8691-db94-4e29-8337-f8ef553716ca", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.callback_query.from.id;\n// Reset the cart for this user\nglobal.set(`cart_${userId}`, { items: [], total: 0 });\nreturn [{\n  json: {\n    chatId: userId,\n    message: \"🗑️ Your cart has been emptied.\",\n    message_id: $json.callback_query.message.message_id\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-576, 1440], "id": "e01e19d7-ee28-426d-91f0-4359b016c121", "name": "Clear Cart Logic"}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-352, 1440], "id": "5d0ba199-660b-4000-bc62-c6cfcc9041c8", "name": "Send Cleared Cart Confirmation", "webhookId": "7e684474-03e0-481f-a862-15aa9ae361cf", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}, {"node": "Is Callback Query?", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Send Welcome Message", "type": "main", "index": 0}], [{"node": "Get Vendors", "type": "main", "index": 0}], [{"node": "Get Vendors", "type": "main", "index": 0}], [{"node": "Get Vendors", "type": "main", "index": 0}], [{"node": "Send Welcome Message", "type": "main", "index": 0}], [{"node": "Get Vendors", "type": "main", "index": 0}], [{"node": "Get Vendor Details", "type": "main", "index": 0}], [{"node": "Extract Item Info", "type": "main", "index": 0}], [{"node": "Get Menu Items", "type": "main", "index": 0}], [{"node": "Answer Pre-Checkout Query", "type": "main", "index": 0}], [{"node": "Finalize Order", "type": "main", "index": 0}], [{"node": "View Cart Details", "type": "main", "index": 0}], [{"node": "Prepare Checkout", "type": "main", "index": 0}], [{"node": "Clear Cart Logic", "type": "main", "index": 0}]]}, "Is Callback Query?": {"main": [[{"node": "Answer Callback Query", "type": "main", "index": 0}]]}, "Get Vendors": {"main": [[{"node": "Generate V<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Vendor Buttons": {"main": [[{"node": "Send Dynamic Vendor List", "type": "main", "index": 0}]]}, "Get Vendor Details": {"main": [[{"node": "Generate <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Menu Buttons": {"main": [[{"node": "Send Dynamic Vendor Details", "type": "main", "index": 0}]]}, "Get Menu Items": {"main": [[{"node": "Format Menu Items", "type": "main", "index": 0}]]}, "Format Menu Items": {"main": [[{"node": "Send Dynamic Menu Items", "type": "main", "index": 0}]]}, "Extract Item Info": {"main": [[{"node": "Get Item Details", "type": "main", "index": 0}]]}, "Get Item Details": {"main": [[{"node": "Add Item to Cart", "type": "main", "index": 0}]]}, "Add Item to Cart": {"main": [[{"node": "Send 'Added to <PERSON><PERSON><PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "View Cart Details": {"main": [[{"node": "Send Cart Details", "type": "main", "index": 0}]]}, "Prepare Checkout": {"main": [[{"node": "Send Invoice", "type": "main", "index": 0}]]}, "Finalize Order": {"main": [[{"node": "Send Payment Confirmation", "type": "main", "index": 0}]]}, "Clear Cart Logic": {"main": [[{"node": "Send Cleared Cart Confirmation", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "815921b7-7e6f-4ac1-a9d0-fb422f88e797", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "yXSgMYBuZvK8mqvY", "tags": []}