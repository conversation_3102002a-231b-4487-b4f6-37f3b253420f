{"name": "ELOH Processing Bot - Merged V2+V3 (Updated)", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "6f89e99a-36d9-44b7-bda8-7c335c576acf", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1472, -144], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "c4966a0f-c6c7-4eff-9825-febc5c6771c8", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1280, -144]}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet lastName = (message && message.from && message.from.last_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.last_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.last_name);\n\nlet isCallback = !!callbackQuery;\nlet isNewChatMember = !!chatMember;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    unifiedLastName: lastName,\n    isCallback: isCallback,\n    isNewChatMember: isNewChatMember\n  }\n};"}, "id": "standardize-user-data", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1100, -144]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}], "combineOperation": "any"}, "options": {}}, "id": "payment-event-check", "name": "Payment Event Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-900, -144]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}], "combineOperation": "any"}, "options": {}}, "id": "successful-payment-check", "name": "Successful Payment Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-700, -300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}, {"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "undefined", "operation": {"type": "string", "operation": "notEqual"}}, {"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "null", "operation": {"type": "string", "operation": "notEqual"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-user-id", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-700, -144]}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}]}}, "id": "check-existing-user", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-500, -144], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "equal"}}]}, "options": {}}, "id": "user-exists-check", "name": "User Exists Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-350, -144]}, {"parameters": {"operation": "insert", "tableId": "users", "columns": {"columns": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}, {"keyName": "username", "value": "={{ $json.unifiedUsername || '' }}"}, {"keyName": "name", "value": "={{ ((($json.unifiedFirstName || '') + ' ' + ($json.unifiedLastName || '')).trim()) || 'User' }}"}, {"keyName": "is_verified_investor", "value": false}, {"keyName": "role", "value": "investor"}]}}, "id": "create-new-user", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-200, -200], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true\n  }\n};"}, "id": "mark-new-user", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-50, -200]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: false\n  }\n};"}, "id": "mark-existing-user", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-50, -100]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start", "output": 0}, {"value2": "main", "output": 0}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}]}}, "id": "740926b8-52dd-4232-ba7f-e4a67990593f", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-200, -144], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "// Enhanced dynamic menu builder using database-driven approach\nconst user = $json.user;\nconst isAdmin = user?.role === 'admin';\nconst isVerified = user?.is_verified_investor;\n\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = isVerified ? '💎 Verified Investor' : '👤 Community Member';\n\n// This will be enhanced to load from bot_menu table\nconst dynamicKeyboard = [\n  [\n    { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n    { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n  ],\n  [\n    { text: \"� Order Services\", callback_data: \"browse_vendors\" },\n    { text: \"🛍️ My Cart\", callback_data: \"view_cart\" }\n  ],\n  [\n    { text: \"📋 My Orders\", callback_data: \"view_orders\" },\n    { text: \"💝 Donate\", callback_data: \"donate\" }\n  ]\n];\n\n// Add admin-only buttons\nif (isAdmin) {\n  dynamicKeyboard.push([\n    { text: \"⚙️ Admin Panel\", callback_data: \"admin_panel\" },\n    { text: \"📊 Analytics\", callback_data: \"admin_analytics\" }\n  ]);\n}\n\n// Add external links\ndynamicKeyboard.push([\n  { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n  { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n]);\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n• User ID: ${user?.user_id}\\n\\n*SERVICES & ORDERS*\\n� Browse Services | �️ View Cart | 📋 Order History\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*COMMUNITY*\\n�️ Roadmap | � Donate | 🏭 Operations`,\n  reply_markup: {\n    inline_keyboard: dynamicKeyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "dynamic-menu-builder", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, -220]}, {"parameters": {"chatId": "={{ $json.chatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {"messageId": "={{ $json.messageId }}", "method": "={{ $json.messageId ? 'editMessageText' : 'sendMessage' }}"}}, "id": "send-main-menu", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [200, -220], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "c777d33a-0163-4527-a2a1-7cf92c31a980", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [0, -50], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "82aa6a7d-1d10-4d7f-bfac-99ac26f7e7c3", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, -50]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "73cb6ff2-ccf4-4f7d-a87d-00b85cbc1aa1", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [400, -50], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "a692b5e5-1442-4e6c-b7db-7ddc921942a5", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [0, 150], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}]}}, "id": "********-89c2-4a65-b29a-f01e29e0684f", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [0, 50], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "8aa01e94-a179-410a-8aaa-adae175594fd", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [200, 50]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Hello {{ $json[0]?.name || 'Investor' }}! Your current investment value is: ${{ $json[0]?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "5e32643e-66a0-4a32-88c9-bfedcff5b529", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [400, 0], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "2268e8e8-aaf3-4c69-90ac-30db9546dcf7", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [400, 100], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst serviceButtons = [\n  [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n  [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n  [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n  [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n];\n\nconst keyboard = [...serviceButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "service-menu-builder", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, 300]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {"messageId": "={{ $json.messageId }}", "method": "={{ $json.messageId ? 'editMessageText' : 'sendMessage' }}"}}, "id": "send-services-menu", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [200, 300], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst donateButtons = [\n  [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n  [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n  [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n  [{ text: \"Custom Amount\", callback_data: \"donate_custom\" }]\n];\n\nconst keyboard = [...donateButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "donate-menu-builder", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, 450]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {"messageId": "={{ $json.messageId }}", "method": "={{ $json.messageId ? 'editMessageText' : 'sendMessage' }}"}}, "id": "send-donate-menu", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [200, 450], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons.", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "unhandled-input", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-200, -300], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "❌ *Error: Invalid User Data*\n\nSorry, we couldn't process your request due to invalid user information. Please try again or contact support if the issue persists.\n\n🔄 Use /start to restart the bot.", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "invalid-user-error", "name": "Invalid User Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-500, -300], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "💳 *Payment Processing*\n\nWe've received your payment request and are processing it. You'll receive a confirmation shortly.\n\n⏳ Please wait while we handle your transaction...", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "handle-payment-event", "name": "Handle Payment Event", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-700, -450], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "✅ *Payment Successful!*\n\nThank you for your payment! Your transaction has been completed successfully.\n\n📧 You'll receive a confirmation email shortly.\n🏠 Use /start to return to the main menu.", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "handle-successful-payment", "name": "Handle Successful Payment", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-500, -450], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "🔧 *Database Error*\n\nWe're experiencing technical difficulties accessing our database. Please try again in a few moments.\n\n🔄 Use /start to restart the bot.\n📞 If the problem persists, please contact support.", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "database-error-handler", "name": "Database Error <PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-300, -300], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "❌ *User Registration Error*\n\nWe couldn't create your account due to a technical issue. This might be because:\n\n• Your account already exists\n• Database connectivity issues\n• Invalid user data\n\n🔄 Please try /start again or contact support if the issue persists.", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "user-creation-error", "name": "User Creation Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-100, -300], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "connections": {"6f89e99a-36d9-44b7-bda8-7c335c576acf": {"main": [[{"node": "c4966a0f-c6c7-4eff-9825-febc5c6771c8", "type": "main", "index": 0}]]}, "c4966a0f-c6c7-4eff-9825-febc5c6771c8": {"main": [[{"node": "standardize-user-data", "type": "main", "index": 0}]]}, "standardize-user-data": {"main": [[{"node": "payment-event-check", "type": "main", "index": 0}]]}, "payment-event-check": {"main": [[{"node": "handle-payment-event", "type": "main", "index": 0}], [{"node": "successful-payment-check", "type": "main", "index": 0}]]}, "successful-payment-check": {"main": [[{"node": "handle-successful-payment", "type": "main", "index": 0}], [{"node": "validate-user-id", "type": "main", "index": 0}]]}, "validate-user-id": {"main": [[{"node": "check-existing-user", "type": "main", "index": 0}], [{"node": "invalid-user-error", "type": "main", "index": 0}]]}, "check-existing-user": {"main": [[{"node": "user-exists-check", "type": "main", "index": 0}]], "error": [[{"node": "database-error-handler", "type": "main", "index": 0}]]}, "user-exists-check": {"main": [[{"node": "create-new-user", "type": "main", "index": 0}], [{"node": "mark-existing-user", "type": "main", "index": 0}]]}, "create-new-user": {"main": [[{"node": "mark-new-user", "type": "main", "index": 0}]], "error": [[{"node": "user-creation-error", "type": "main", "index": 0}]]}, "mark-new-user": {"main": [[{"node": "740926b8-52dd-4232-ba7f-e4a67990593f", "type": "main", "index": 0}]]}, "mark-existing-user": {"main": [[{"node": "740926b8-52dd-4232-ba7f-e4a67990593f", "type": "main", "index": 0}]]}, "740926b8-52dd-4232-ba7f-e4a67990593f": {"main": [[{"node": "dynamic-menu-builder", "type": "main", "index": 0}], [{"node": "c777d33a-0163-4527-a2a1-7cf92c31a980", "type": "main", "index": 0}], [{"node": "********-89c2-4a65-b29a-f01e29e0684f", "type": "main", "index": 0}], [{"node": "service-menu-builder", "type": "main", "index": 0}], [{"node": "donate-menu-builder", "type": "main", "index": 0}], [{"node": "unhandled-input", "type": "main", "index": 0}]]}, "dynamic-menu-builder": {"main": [[{"node": "send-main-menu", "type": "main", "index": 0}]]}, "c777d33a-0163-4527-a2a1-7cf92c31a980": {"main": [[{"node": "82aa6a7d-1d10-4d7f-bfac-99ac26f7e7c3", "type": "main", "index": 0}]]}, "82aa6a7d-1d10-4d7f-bfac-99ac26f7e7c3": {"main": [[{"node": "73cb6ff2-ccf4-4f7d-a87d-00b85cbc1aa1", "type": "main", "index": 0}]]}, "********-89c2-4a65-b29a-f01e29e0684f": {"main": [[{"node": "8aa01e94-a179-410a-8aaa-adae175594fd", "type": "main", "index": 0}]]}, "8aa01e94-a179-410a-8aaa-adae175594fd": {"main": [[{"node": "5e32643e-66a0-4a32-88c9-bfedcff5b529", "type": "main", "index": 0}], [{"node": "2268e8e8-aaf3-4c69-90ac-30db9546dcf7", "type": "main", "index": 0}]]}, "service-menu-builder": {"main": [[{"node": "send-services-menu", "type": "main", "index": 0}]]}, "donate-menu-builder": {"main": [[{"node": "send-donate-menu", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-12-09T00:00:00.000Z", "versionId": "1"}