{"name": "My workflow", "nodes": [{"parameters": {"updates": ["message", "callback_query", "pre_checkout_query", "successful_payment"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-2256, 2064], "id": "be714ddb-ad11-4fa3-b747-953457d20e91", "name": "<PERSON>eg<PERSON>", "webhookId": "23b39a94-f325-4058-9e19-68f6cc044920", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "{{$json[\"message\"][\"text\"]}}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}, "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Welcome"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "62d8cb3b-43ab-4f99-80e4-6f1741b46d42", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/vendors", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Vend<PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-query-condition", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "Vend<PERSON>", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fa012ef9-5b99-4922-b134-fca073f90994", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/menu", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "27f37457-3e10-4eb8-a9cd-dbceef71f5ad", "leftValue": "={{$json[\"message\"][\"text\"]}}", "rightValue": "/order", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Order"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-browse-menu", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "browse_menu", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-place-order", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "place_order", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackOrder"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-back-to-start", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "back_to_start", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackBackToStart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-back-to-vendors", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "back_to_vendors", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CallbackBackToVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-vendor-selection", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "vendor_", "operator": {"type": "string", "operation": "startsWith"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-mains", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_mains", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "MainsMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-drinks", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_drinks", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DrinksMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-desserts", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_desserts", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DessertsMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "callback-category-sides", "leftValue": "={{$json[\"callback_query\"][\"data\"]}}", "rightValue": "category_sides", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "SidesMenu"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "add_", "operator": {"type": "string", "operation": "startsWith"}, "id": "add-item-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "AddItemToCart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "menu_", "operator": {"type": "string", "operation": "startsWith"}, "id": "vendor-menu-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorMenuSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "order_", "operator": {"type": "string", "operation": "startsWith"}, "id": "vendor-order-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorOrderSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "pre_checkout_query", "operator": {"type": "string", "operation": "equals"}, "id": "pre-checkout-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PreCheckoutQuery"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "successful_payment", "operator": {"type": "string", "operation": "equals"}, "id": "successful-payment-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "SuccessfulPayment"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "view_cart", "operator": {"type": "string", "operation": "equals"}, "id": "view-cart-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ViewCart"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "checkout", "operator": {"type": "string", "operation": "equals"}, "id": "checkout-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Checkout"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "clear_cart", "operator": {"type": "string", "operation": "equals"}, "id": "clear-cart-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ClearCart"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-2032, 976], "id": "14cdcc95-a9df-430e-82a5-3310174df644", "name": "Switch"}, {"parameters": {"chatId": "={{ $json.message.from.id }}", "text": "Vend<PERSON>", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "Mama J’s Kitchen", "additionalFields": {"callback_data": "vendor_mama_js"}}, {"text": "Island Bites", "additionalFields": {"callback_data": "vendor_island_bites"}}, {"text": "Street Grill", "additionalFields": {"callback_data": "vendor_street_grill"}}, {"text": "Back", "additionalFields": {"callback_data": "back_to_start"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 144], "id": "877397ac-6c0b-4769-99a2-70d44de70cb7", "name": "Send a text message1", "webhookId": "d5518a68-7f3f-4c88-ab87-3efc9adfbf1d", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "menu – pick a category ", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "Mains", "additionalFields": {"callback_data": "category_mains"}}, {"text": "Drinks", "additionalFields": {"callback_data": "category_drinks"}}, {"text": "Desserts ", "additionalFields": {"callback_data": "category_desserts"}}, {"text": "Sides", "additionalFields": {"callback_data": "category_sides"}}, {"text": "Back to Vendors", "additionalFields": {"callback_data": "back_to_vendors"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 528], "id": "32581c59-2e4a-4b7a-9194-12cc0c9f9587", "name": "Send a text message2", "webhookId": "5f036f4b-18b1-4d98-89c4-4458bf8a05c1", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "Order – confirm pickup or delivery", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "Confirm Pickup", "additionalFields": {"callback_data": "confirm_pickup"}}, {"text": "Confirm Delivery", "additionalFields": {"callback_data": "confirm_delivery"}}, {"text": "Cancel Order", "additionalFields": {"callback_data": "cancel_order"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 1104], "id": "a4bd9116-7f56-4a63-9a46-096fa71871e3", "name": "Send a text message3", "webhookId": "276299c6-4d14-4a62-aa0e-7cd69f1c140f", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message.from.id }}", "text": "Choose an option below to get started.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Vendors", "additionalFields": {"callback_data": "Vend<PERSON>"}}, {"text": "<PERSON><PERSON><PERSON> Menu", "additionalFields": {"callback_data": "browse_menu"}}, {"text": "Place Order", "additionalFields": {"callback_data": "place_order"}}]}}]}, "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, -48], "id": "b9052560-66b7-46b1-ad14-0874fa41be21", "name": "Send a text message", "webhookId": "280904cc-4b9d-4078-be23-5bccfc3a962f", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "callback-exists-check", "leftValue": "={{ $json.callback_query }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "b230cbcb-d644-450d-966e-feacaa6c5aa0", "name": "Callback Query Filter", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2032, 2832]}, {"parameters": {"resource": "callback", "queryId": "={{ $json.callback_query.id }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 2832], "id": "6eff62c4-cb58-49b4-ae0e-3407f05a673a", "name": "Answer Query a callback", "webhookId": "1c00e020-a799-4f49-ba33-87ff8b2f3541", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🍽️ **Mama J's Kitchen**\n\nSpecializing in authentic Caribbean cuisine with a modern twist. Known for their jerk chicken and curry dishes.\n\n📍 Location: Downtown Food Court\n⏰ Hours: 11:00 AM - 9:00 PM\n⭐ Rating: 4.8/5", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Menu", "additionalFields": {"callback_data": "menu_mama_js"}}, {"text": "Place Order", "additionalFields": {"callback_data": "order_mama_js"}}]}}, {"row": {"buttons": [{"text": "← Back to Vendors", "additionalFields": {"callback_data": "back_to_vendors"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2400, 496], "id": "d8984513-7956-4b6d-9975-69d02dbcd1e2", "name": "Mama J's Kitchen Details", "webhookId": "bac7b815-1960-437f-bc4e-69d56b850ffa", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🏝️ **Island Bites**\n\nFresh seafood and tropical flavors from the Caribbean islands. Famous for their fish tacos and coconut shrimp.\n\n📍 Location: Beachside Plaza\n⏰ Hours: 12:00 PM - 10:00 PM\n⭐ Rating: 4.6/5", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Menu", "additionalFields": {"callback_data": "menu_island_bites"}}, {"text": "Place Order", "additionalFields": {"callback_data": "order_island_bites"}}]}}, {"row": {"buttons": [{"text": "← Back to Vendors", "additionalFields": {"callback_data": "back_to_vendors"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2400, 720], "id": "fdd71bdb-1a13-4cb6-9e67-e6cc38dc824f", "name": "Island Bites Details", "webhookId": "f4c16dfe-cfa3-4d64-a81a-04196b035f10", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🔥 **Street Grill**\n\nAuthentic street food with bold flavors. Specializing in grilled meats, plantains, and traditional sides.\n\n📍 Location: Market Street Corner\n⏰ Hours: 10:00 AM - 11:00 PM\n⭐ Rating: 4.7/5", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Menu", "additionalFields": {"callback_data": "menu_street_grill"}}, {"text": "Place Order", "additionalFields": {"callback_data": "order_street_grill"}}]}}, {"row": {"buttons": [{"text": "← Back to Vendors", "additionalFields": {"callback_data": "back_to_vendors"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2400, 944], "id": "631af590-76e9-4589-af11-c3f130f25514", "name": "Street Grill Details", "webhookId": "e0603d2a-f39d-4853-8837-2ec0eb22fe82", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🍽️ **MAINS MENU**\n\nOur signature main dishes featuring authentic Caribbean flavors:\n\n🔥 **Jerk Chicken** - $18.99\nSpicy marinated chicken grilled to perfection\n\n🍛 **Curry Goat** - $22.99\nTender goat meat in aromatic curry sauce\n\n🐟 **Escovitch Fish** - $19.99\nFried fish with pickled vegetables\n\n🍖 **Oxtail Stew** - $24.99\nSlow-cooked oxtail in rich gravy\n\n🌶️ **Jerk Pork** - $20.99\nSpicy grilled pork with island seasonings", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🛒 Add Jerk Chicken", "additionalFields": {"callback_data": "add_jerk_chicken"}}, {"text": "🛒 Add Curry Goat", "additionalFields": {"callback_data": "add_curry_goat"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Escovitch Fish", "additionalFields": {"callback_data": "add_escovitch_fish"}}, {"text": "🛒 Add Oxtail Stew", "additionalFields": {"callback_data": "add_oxtail_stew"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Jerk Pork", "additionalFields": {"callback_data": "add_jerk_pork"}}, {"text": "← Back to Categories", "additionalFields": {"callback_data": "browse_menu"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 336], "id": "493e27cc-695f-452c-bdd0-d559012752fa", "name": "Mains Menu", "webhookId": "4c67983d-0257-4db9-956b-f6a37e2c6f51", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🥤 **DRINKS MENU**\n\nRefreshing beverages to complement your meal:\n\n🥭 **Mango Juice** - $4.99\nFresh tropical mango juice\n\n🥥 **Coconut Water** - $3.99\nNatural coconut water\n\n🍹 **Rum Punch** - $8.99\nTraditional Caribbean cocktail\n\n☕ **Blue Mountain Coffee** - $5.99\nPremium Jamaican coffee\n\n🧊 **Sorrel Drink** - $4.49\nSpiced hibiscus beverage\n\n💧 **Water** - $1.99\nBottled spring water", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🛒 Add Mango Juice", "additionalFields": {"callback_data": "add_mango_juice"}}, {"text": "🛒 Add Coconut Water", "additionalFields": {"callback_data": "add_coconut_water"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Rum Punch", "additionalFields": {"callback_data": "add_rum_punch"}}, {"text": "🛒 Add Coffee", "additionalFields": {"callback_data": "add_coffee"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Sorrel Drink", "additionalFields": {"callback_data": "add_sorrel"}}, {"text": "← Back to Categories", "additionalFields": {"callback_data": "browse_menu"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 1872], "id": "119ff56c-3e3e-4209-93cd-2e065177497c", "name": "Dr<PERSON>s <PERSON>u", "webhookId": "b5510c41-bb55-47d4-9ce7-c2b2ef8d9415", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🍰 **DESSERTS MENU**\n\nSweet treats to end your meal perfectly:\n\n🥥 **Coconut Drops** - $6.99\nTraditional coconut candy\n\n🍌 **Banana Bread** - $5.99\nMoist homemade banana bread\n\n🧁 **Rum Cake** - $7.99\nRich cake soaked in Caribbean rum\n\n🍮 **Sweet Potato Pudding** - $6.49\nCreamy spiced pudding\n\n🥧 **Plantain Tart** - $6.99\nSweet plantain in pastry crust\n\n🍨 **Coconut Ice Cream** - $4.99\nHomemade tropical ice cream", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🛒 Add Coconut Drops", "additionalFields": {"callback_data": "add_coconut_drops"}}, {"text": "🛒 Add Banana Bread", "additionalFields": {"callback_data": "add_banana_bread"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Rum Cake", "additionalFields": {"callback_data": "add_rum_cake"}}, {"text": "🛒 Add Sweet Potato Pudding", "additionalFields": {"callback_data": "add_sweet_potato_pudding"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Plantain Tart", "additionalFields": {"callback_data": "add_plantain_tart"}}, {"text": "← Back to Categories", "additionalFields": {"callback_data": "browse_menu"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 2256], "id": "4f4dd2ef-5f14-434e-9375-565e08a2bbed", "name": "Desserts Menu", "webhookId": "9050c233-2af8-4e13-a405-e95e4d420f5f", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "🥗 **SIDES MENU**\n\nPerfect accompaniments to your main dish:\n\n🍚 **Rice & Peas** - $4.99\nCoconut rice with kidney beans\n\n🍌 **Fried Plantains** - $3.99\nSweet caramelized plantains\n\n🥬 **Steamed Vegetables** - $4.49\nSeasonal mixed vegetables\n\n🍞 **Festival** - $2.99\nSweet fried dumplings\n\n🥔 **Yam & Dumplings** - $5.49\nBoiled yam with flour dumplings\n\n🌶️ **Coleslaw** - $3.49\nFresh cabbage salad with spicy dressing", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🛒 Add Rice & Peas", "additionalFields": {"callback_data": "add_rice_peas"}}, {"text": "🛒 Add Fried Plantains", "additionalFields": {"callback_data": "add_plantains"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Steamed Vegetables", "additionalFields": {"callback_data": "add_vegetables"}}, {"text": "🛒 Add Festival", "additionalFields": {"callback_data": "add_festival"}}]}}, {"row": {"buttons": [{"text": "🛒 Add Yam & Dumplings", "additionalFields": {"callback_data": "add_yam_dumplings"}}, {"text": "← Back to Categories", "additionalFields": {"callback_data": "browse_menu"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1808, 1296], "id": "0904269d-8203-4bf9-b9d8-f1f8f752dbb3", "name": "<PERSON><PERSON> <PERSON>u", "webhookId": "61df2ffe-b17c-4e3d-989b-f03fd37be01e", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "let vendor_buttons = [];\n\nfor (const vendor of items[0].json.data) {\n  vendor_buttons.push({\n    text: vendor.name,\n    additionalFields: {\n      callback_data: `vendor_${vendor.id}`\n    }\n  });\n}\n\nvendor_buttons.push({\n  text: \"← Back to Start\",\n  additionalFields: {\n    callback_data: \"back_to_start\"\n  }\n});\n\nreturn [\n  {\n    json: {\n      text: \"Please select a vendor:\",\n      inline_keyboard: [vendor_buttons]\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1584, 1680], "id": "********-0792-4754-869b-4cc43a837e39", "name": "Generate V<PERSON><PERSON>"}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1360, 1680], "id": "3e420425-0c61-4a02-8ced-b3a5c8db1313", "name": "Send Dynamic Vendor List", "webhookId": "6467ddee-cee8-4d3f-a4ff-c28e31fe9cab", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "__CUSTOM_API_CALL__"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1808, 720], "id": "ddeb72bf-14f2-4935-a5c7-ef4683d7e8a7", "name": "Get Vendor Details", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"resource": "__CUSTOM_API_CALL__"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1584, 720], "id": "1a0fad51-3264-4cc1-b636-e8f11928c7e6", "name": "<PERSON><PERSON><PERSON>", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "let vendor = items[0].json.data[0];\nlet menus = items[1].json.data;\n\nlet menuButtons = menus.map(menu => ({\n  text: menu.name,\n  additionalFields: {\n    callback_data: `menu_${menu.id}`\n  }\n}));\n\nlet backButton = {\n  text: \"← Back to Vendors\",\n  additionalFields: {\n    callback_data: \"back_to_vendors\"\n  }\n};\n\nmenuButtons.push(backButton);\n\nlet messageText = `**${vendor.name}**\\n\\n${vendor.description}\\n\\n**Website:** ${vendor.website_url || 'N/A'}\\n\\n*Select a menu category:*`;\n\nreturn [\n  {\n    json: {\n      text: messageText,\n      inline_keyboard: [menuButtons]\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1360, 720], "id": "f8dc85f0-6c19-4a30-89ed-65288cea615b", "name": "Generate <PERSON><PERSON>"}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1136, 720], "id": "9ef31286-4839-4399-9a52-a2d3599939c1", "name": "Send Dynamic Vendor Details", "webhookId": "366898d8-e16e-49b6-9ae9-a8277b1ce6fc", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "__CUSTOM_API_CALL__"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1808, 2640], "id": "0a9da906-0827-4d97-b9b7-67fdcd65e84d", "name": "Get Menu Items", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "let items = items[0].json.data;\n\nconst categorizedItems = items.reduce((acc, item) => {\n  const category = item.category || 'Uncategorized';\n  if (!acc[category]) {\n    acc[category] = [];\n  }\n  acc[category].push(item);\n  return acc;\n}, {});\n\nlet outputMessage = \"\";\nfor (const category in categorizedItems) {\n  outputMessage += `*${category}*\\n`;\n  categorizedItems[category].forEach(item => {\n    outputMessage += `- ${item.name} ($${item.price.toFixed(2)})\\n`;\n  });\n  outputMessage += \"\\n\";\n}\n\noutputMessage += \"\\n_Tap an item to add to your cart_\\n\";\n\nlet itemButtons = items.map(item => ({\n  text: `${item.name} - $${item.price.toFixed(2)}`,\n  additionalFields: {\n    callback_data: `add_${item.id}`\n  }\n}));\n\nitemButtons.push({\n  text: \"← Back to Menus\",\n  additionalFields: {\n    callback_data: \"back_to_menus\"\n  }\n});\n\nreturn [\n  {\n    json: {\n      text: outputMessage,\n      inline_keyboard: [itemButtons]\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1584, 2640], "id": "f56d032d-e2d9-4101-bd1b-704d8344994b", "name": "Format Menu Items"}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}, {"additionalFields": {}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1360, 2640], "id": "5948eb4d-db00-49e4-9c0d-55f52fa7212d", "name": "Send Dynamic Menu Items", "webhookId": "375367a1-0c71-4efa-84c9-0372c3cc6192", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "// This function will be called after getting item details from Supabase\n// Extract item ID from callback data\nconst callbackData = $json.callback_query.data;\nconst itemId = callbackData.replace('add_', '');\n\nconst userId = $json.callback_query.from.id;\n\n// Return the item ID and user ID for the next node to fetch item details\nreturn [{\n  json: {\n    itemId: itemId,\n    userId: userId,\n    callback_query: $json.callback_query\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1808, 912], "id": "b35c8d29-dd04-452c-b2a7-f6124928ec14", "name": "Extract Item Info"}, {"parameters": {"resource": "__CUSTOM_API_CALL__"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1584, 912], "id": "fea9c098-1fc2-438d-9902-8c50910f9e0a", "name": "Get Item Details", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"functionCode": "// Get item details from Supabase response\nconst item = items[0].json.data[0];\nconst userId = items[0].json.userId;\n\nif (!item) {\n  return [{ json: { error: 'Item not found', itemId: items[0].json.itemId } }];\n}\n\n// Get existing cart from global variable or initialize\nlet userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\n// Check if item already exists in cart\nlet existingItemIndex = userCart.items.findIndex(cartItem => cartItem.id === item.id);\n\nif (existingItemIndex > -1) {\n  // Increase quantity\n  userCart.items[existingItemIndex].quantity += 1;\n} else {\n  // Add new item\n  userCart.items.push({\n    id: item.id,\n    name: item.name,\n    price: item.price,\n    quantity: 1,\n    category: item.category\n  });\n}\n\n// Recalculate total\nuserCart.total = userCart.items.reduce((sum, cartItem) => sum + (cartItem.price * cartItem.quantity), 0);\n\n// Save cart back to global variable\nglobal.set(`cart_${userId}`, userCart);\n\nreturn [{\n  json: {\n    success: true,\n    message: `${item.name} added to cart! 🛒`,\n    cart: userCart,\n    userId: userId,\n    callback_query: items[0].json.callback_query\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1360, 912], "id": "0f8cc2b7-2758-4227-99b5-2d526fb21eff", "name": "Add Item to Cart"}, {"parameters": {"chatId": "={{ $json.callback_query.from.id }}", "text": "={{ $json.message }}\\n\\n🛒 **Your Cart:**\\n{{ $json.cart.items.map(item => `• ${item.name} x${item.quantity} - $${(item.price * item.quantity).toFixed(2)}`).join('\\n') }}\\n\\n💰 **Total: ${{ $json.cart.total.toFixed(2) }}**", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🛒 View Full Cart", "additionalFields": {"callback_data": "view_cart"}}, {"text": "💳 Checkout", "additionalFields": {"callback_data": "checkout"}}]}}, {"row": {"buttons": [{"text": "← Continue Shopping", "additionalFields": {"callback_data": "browse_menu"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1136, 912], "id": "9e2f0a5c-8332-4605-85a4-708cb6dff1fb", "name": "Send Cart Update", "webhookId": "b60246f0-015e-481a-9563-e08da26fed4a", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  return [{\n    json: {\n      isEmpty: true,\n      message: \"🛒 Your cart is empty!\\n\\nStart browsing our delicious Caribbean menu to add items.\"\n    }\n  }];\n}\n\nlet cartText = \"🛒 **Your Shopping Cart**\\n\\n\";\nuserCart.items.forEach((item, index) => {\n  cartText += `${index + 1}. **${item.name}**\\n`;\n  cartText += `   Quantity: ${item.quantity}\\n`;\n  cartText += `   Price: $${item.price.toFixed(2)} each\\n`;\n  cartText += `   Subtotal: $${(item.price * item.quantity).toFixed(2)}\\n\\n`;\n});\n\ncartText += `💰 **Total: $${userCart.total.toFixed(2)}**`;\n\nreturn [{\n  json: {\n    isEmpty: false,\n    cartText: cartText,\n    cart: userCart,\n    userId: userId\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1808, 1488], "id": "7f255eb3-35f8-4ed3-9650-1f9cca1fa9fd", "name": "View Cart Details"}, {"parameters": {"chatId": "={{ $json.callback_query.from.id }}", "text": "={{ $json.cartText }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💳 Proceed to Checkout", "additionalFields": {"callback_data": "checkout"}}, {"text": "🗑️ Clear Cart", "additionalFields": {"callback_data": "clear_cart"}}]}}, {"row": {"buttons": [{"text": "← Continue Shopping", "additionalFields": {"callback_data": "browse_menu"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1584, 1488], "id": "fd4eddcd-f466-44d4-8391-21006214abfc", "name": "Send Cart Details", "webhookId": "9ac0bfbc-8cee-42c9-b973-2218edf6171e", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  return []; // Stop execution if cart is empty\n}\n\n// Create the 'prices' array for the invoice\n// Telegram requires the amount in the smallest currency unit (e.g., cents)\nconst prices = userCart.items.map(item => ({\n  label: `${item.name} x${item.quantity}`,\n  amount: Math.round(item.price * item.quantity * 100)\n}));\n\n// Prepare summary text for the invoice description\nlet invoiceDescription = \"Your order includes:\\n\";\nuserCart.items.forEach(item => {\n  invoiceDescription += `• ${item.name} x${item.quantity}\\n`;\n});\ninvoiceDescription += `\\nTotal: $${userCart.total.toFixed(2)}`;\n\nreturn [{\n  json: {\n    prices: prices,\n    userId: userId,\n    invoiceDescription: invoiceDescription,\n    payload: `checkout-${userId}-${Date.now()}`\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1808, 2064], "id": "b5a3ca1e-6c4e-4815-9158-5eb88d234217", "name": "Prepare Checkout"}, {"parameters": {"chatId": "={{ $json.callback_query.from.id }}", "text": "={{ $json.checkoutText }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💳 Pay with Card", "additionalFields": {"callback_data": "pay_card"}}, {"text": "💰 Cash on Delivery", "additionalFields": {"callback_data": "pay_cash"}}]}}, {"row": {"buttons": [{"text": "📍 Pickup", "additionalFields": {"callback_data": "pickup"}}, {"text": "🚚 Delivery", "additionalFields": {"callback_data": "delivery"}}]}}, {"row": {"buttons": [{"text": "← Back to Cart", "additionalFields": {"callback_data": "view_cart"}}]}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-784, 1760], "id": "5a7976ee-e1c2-4b73-9e60-a4c3c902d435", "name": "Send Checkout Options", "webhookId": "ed6aade5-1ff7-4a05-b9b6-9bc51c122db2", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.callback_query.from.id;\n// Reset the cart for this user\nglobal.set(`cart_${userId}`, { items: [], total: 0 });\nreturn [{\n  json: {\n    chatId: userId,\n    message: \"🗑️ Your cart has been emptied.\",\n    message_id: $json.callback_query.message.message_id\n  }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1808, 2448], "id": "d913f4e4-6c63-4c55-8d6b-70593c135565", "name": "Clear Cart Logic"}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1584, 2448], "id": "11fbf143-ce5e-4aa7-a201-2ee62f5b658f", "name": "Send Cleared Cart Confirmation", "webhookId": "f0001109-d469-481b-bd6a-6ff28c301a15", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "invoice"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1584, 2064], "id": "b1ff9b20-575b-48d5-9d12-8e7068e71c1d", "name": "Send Invoice", "webhookId": "fa84a062-b4d2-4f3a-8138-37f9514ab2cc", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "preCheck<PERSON><PERSON><PERSON><PERSON>"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-784, 1984], "id": "5e410e70-6dbd-4713-b1cc-7e29dd6c0a9d", "name": "Answer Pre-Checkout Query", "webhookId": "18b29a84-7a93-4709-a9ff-38ae86d9b102", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"functionCode": "const userId = $json.successful_payment.from.id;\n// Clear the user's cart after successful payment\nglobal.set(`cart_${userId}`, { items: [], total: 0 });\n\nconst confirmationMessage = \"✅ Thank you for your payment! Your order has been confirmed and is being prepared.\";\n\nreturn [{\n    json: {\n        chatId: userId,\n        message: confirmationMessage\n    }\n}];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-784, 2208], "id": "bc81279c-3863-4c05-8fd5-59ebb3683b9f", "name": "Finalize Order"}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-560, 2208], "id": "543de2fd-d652-4dca-ab46-db61b84a412f", "name": "Send Payment Confirmation", "webhookId": "2acb0ca5-87f9-475d-acf8-73564cdd8d29", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"operation": "getAll", "tableId": "vendors"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1808, 1680], "id": "3dd8de84-4bc9-42c5-b486-678bf783de54", "name": "Get Vendors1", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-2048, 2064], "id": "d2c08fa6-5acd-4af8-b665-adc880ca3b25", "name": "Telegram Trigger1", "webhookId": "910dc2bf-a3c3-4f5d-9bff-f746adccc2eb", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}, {"node": "Callback Query Filter", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Send a text message", "type": "main", "index": 0}], [{"node": "Get Vendors1", "type": "main", "index": 0}], [{"node": "Get Vendors1", "type": "main", "index": 0}], [{"node": "Send a text message2", "type": "main", "index": 0}], [{"node": "Send a text message3", "type": "main", "index": 0}], [{"node": "Send a text message2", "type": "main", "index": 0}], [{"node": "Send a text message3", "type": "main", "index": 0}], [{"node": "Send a text message", "type": "main", "index": 0}], [{"node": "Send a text message1", "type": "main", "index": 0}], [{"node": "Get Vendor Details", "type": "main", "index": 0}], [{"node": "Mains Menu", "type": "main", "index": 0}], [{"node": "Dr<PERSON>s <PERSON>u", "type": "main", "index": 0}], [{"node": "Desserts Menu", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON> <PERSON>u", "type": "main", "index": 0}], [{"node": "Extract Item Info", "type": "main", "index": 0}], [{"node": "Get Menu Items", "type": "main", "index": 0}], [{"node": "Send a text message3", "type": "main", "index": 0}], [], [], [{"node": "View Cart Details", "type": "main", "index": 0}], [{"node": "Prepare Checkout", "type": "main", "index": 0}], [{"node": "Clear Cart Logic", "type": "main", "index": 0}]]}, "Extract Item Info": {"main": [[{"node": "Get Item Details", "type": "main", "index": 0}]]}, "Get Item Details": {"main": [[{"node": "Add Item to Cart", "type": "main", "index": 0}]]}, "Add Item to Cart": {"main": [[{"node": "Send Cart Update", "type": "main", "index": 0}]]}, "View Cart Details": {"main": [[{"node": "Send Cart Details", "type": "main", "index": 0}]]}, "Prepare Checkout": {"main": [[{"node": "Send Invoice", "type": "main", "index": 0}]]}, "Generate Vendor Buttons": {"main": [[{"node": "Send Dynamic Vendor List", "type": "main", "index": 0}]]}, "Get Vendor Details": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Vendor Menus": {"main": [[{"node": "Generate <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Menu Buttons": {"main": [[{"node": "Send Dynamic Vendor Details", "type": "main", "index": 0}]]}, "Get Menu Items": {"main": [[{"node": "Format Menu Items", "type": "main", "index": 0}]]}, "Format Menu Items": {"main": [[{"node": "Send Dynamic Menu Items", "type": "main", "index": 0}]]}, "Finalize Order": {"main": [[{"node": "Send Payment Confirmation", "type": "main", "index": 0}]]}, "Clear Cart Logic": {"main": [[{"node": "Send Cleared Cart Confirmation", "type": "main", "index": 0}]]}, "Callback Query Filter": {"main": [[{"node": "Answer Query a callback", "type": "main", "index": 0}]]}, "Get Vendors1": {"main": [[{"node": "Generate V<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "91692a3f-a394-4b9f-92ab-5aa465beee6e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "QrP0bJgkM7BfiDj8", "tags": []}