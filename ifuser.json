{"name": "My workflow 3", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "d423979a-efe5-4467-a581-f214f5896b7f", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-2512, -1040], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "8ce836c9-d840-4293-bb2d-b5d6922a99a4", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-2368, -1040]}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet lastName = (message && message.from && message.from.last_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.last_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.last_name);\n\nlet isCallback = !!callbackQuery;\nlet isNewChatMember = !!chatMember;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    unifiedLastName: lastName,\n    isCallback: isCallback,\n    isNewChatMember: isNewChatMember\n  }\n};"}, "id": "4950725b-06d6-423e-a341-c6a383ac807f", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2064, -1024]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}], "combineOperation": "any"}, "options": {}}, "id": "6286135c-c646-4b1c-9d08-63cb5416ea76", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2528, -816]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ typeof $json.unifiedUserId }}", "rightValue": "undefined", "operation": {"type": "string", "operation": "notEqual", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "63a12156-1449-49fa-87d7-205e9be986ba", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2352, -832]}, {"parameters": {"jsCode": "// Debug: Check what data is being passed to user lookup\nconsole.log('Data before user lookup:', JSON.stringify($json, null, 2));\nconsole.log('Unified User ID:', $json.unifiedUserId);\nconsole.log('Type of User ID:', typeof $json.unifiedUserId);\n\nreturn $input.all();"}, "id": "27990ef5-21bf-43b0-8201-991de3c5c706", "name": "Debug Before User Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1664, -608]}, {"parameters": {"jsCode": "// Test Supabase connection by checking if we can reach this point\nconsole.log('=== ABOUT TO QUERY SUPABASE ===');\nconsole.log('User ID to search:', $json.unifiedUserId);\nconsole.log('User ID type:', typeof $json.unifiedUserId);\nconsole.log('Full data object:', JSON.stringify($json, null, 2));\nreturn $input.all();"}, "id": "ef4bb247-d83e-4576-8c36-360cfcdb825f", "name": "Test Supabase Connection", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2208, -1056]}, {"parameters": {"operation": "getAll", "tableId": "users", "returnAll": true, "filters": {"conditions": [{"keyName": "telegram_id", "condition": "eq", "keyValue": "={{ $json.unifiedUserId.toString() }}"}]}, "options": {"queryName": ""}}, "id": "f5950333-c8b0-49e9-b733-6f92dcdf400d", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2016, -832], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}, "alwaysOutputData": true}, {"parameters": {"jsCode": "// Merge Supabase results with original user data\nconst originalData = $input.first().json;\nconst supabaseResults = $input.last().json;\n\nconsole.log('=== MERGING USER DATA ===');\nconsole.log('Original data:', JSON.stringify(originalData, null, 2));\nconsole.log('Supabase results:', JSON.stringify(supabaseResults, null, 2));\n\n// Create merged result\nconst mergedData = {\n  ...originalData,\n  supabaseResults: Array.isArray(supabaseResults) ? supabaseResults : [supabaseResults],\n  length: Array.isArray(supabaseResults) ? supabaseResults.length : (supabaseResults ? 1 : 0),\n  userFound: Array.isArray(supabaseResults) ? supabaseResults.length > 0 : !!supabaseResults\n};\n\nconsole.log('Merged result:', JSON.stringify(mergedData, null, 2));\nconsole.log('=== END MERGE ===');\n\nreturn { json: mergedData };"}, "id": "da6f756b-36d6-4cb8-a06f-ee4b9d57320c", "name": "Merge User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2192, -832]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}, "id": "50af86e1-d69e-4842-9ffc-8e517dfa27bc"}], "combineOperation": "and"}, "options": {"looseTypeValidation": false}}, "id": "c33d95c3-11a8-4df0-a771-0478aaa68922", "name": "User Exists Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1712, -816], "alwaysOutputData": false}, {"parameters": {"tableId": "users", "dataToSend": "autoMapInputData"}, "id": "4452121c-6e79-441f-9c7d-b62808ecf2f9", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1536, -768], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\n// Debug: Log the user creation result\nconsole.log('New user created:', JSON.stringify($json, null, 2));\n\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true,\n    debug_user_creation: true\n  }\n};"}, "id": "68bd2615-a2e9-4bb9-a130-048afe1a5696", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1344, -800]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\n// Debug: Log the existing user data\nconsole.log('Existing user found:', JSON.stringify($json, null, 2));\n\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: false,\n    debug_existing_user: true\n  }\n};"}, "id": "521feb02-18b2-4cc7-8fcd-8952f9169d8d", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1856, -960]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "main"}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}]}}, "id": "06a30f42-422a-4fe6-8c0b-0e80adddad23", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1264, -432], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "const user = $json.user;\nconst isAdmin = user?.role === 'admin';\n\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\nconst staticKeyboard = [\n  [\n    { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n    { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n  ],\n  [\n    { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n    { text: \"💝 Donate\", callback_data: \"donate\" }\n  ],\n  [\n    { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n    { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n  ]\n];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: staticKeyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "a2b2ffad-5c32-4c32-9ede-7e22b50861a2", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-832, -896]}, {"parameters": {"chatId": "={{ $json.chatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "d02f20d5-190e-4a46-9979-67f2346d9e10", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-576, -896], "webhookId": "f7808a34-8de2-4cca-9313-c1ec353740df", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "07624bb1-37be-44cc-aab2-94a64272fea9", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-816, -704], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "cd3341e8-2c66-4ae8-b7ef-b29ac0879cec", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-592, -688]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "515cf5e9-4269-46ef-8b64-98d60d323ee9", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-336, -784], "webhookId": "cf66780a-dbcf-441d-aa1b-e62ffe47170e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "cdbfedd9-6f0b-4204-822a-51d0ae8ac106", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1104, -800], "webhookId": "04b66bdf-bcbc-4144-b12f-6b25d86e4b0c", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "id": "a54f0827-6411-40fe-b9ba-c686e76689e5", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-912, -464], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true", "rightType": "any"}}, {"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "7750299c-4a5c-4883-ac6a-7251e115bfc1", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-208, -176]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Hello {{ $json[0]?.name || 'Investor' }}! Your current investment value is: ${{ $json[0]?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "ce1bdc5c-0d99-49de-8cb0-718274bf22a7", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [16, -224], "webhookId": "3fd54c83-3909-4de6-a53c-34c37f734e3b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "5122b419-1dbc-4de5-9418-e9a1c63405a8", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [256, -96], "webhookId": "e797834f-b962-4a70-ad7c-7d45762ad3aa", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst serviceButtons = [\n  [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n  [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n  [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n  [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n];\n\nconst keyboard = [...serviceButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "a5172450-b58a-4c39-9dc3-d598f2641f66", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-896, -48]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "296c3e51-06ca-4115-b419-526c9cf7af74", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, -48], "webhookId": "ce4c0427-72a9-451e-aa40-4b9dac6bcbf8", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst donateButtons = [\n  [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n  [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n  [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n  [{ text: \"Custom Amount\", callback_data: \"donate_custom\" }]\n];\n\nconst keyboard = [...donateButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "459ea866-e65a-4a4e-b700-36b85481b0d8", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-784, 96]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "963c9c1b-7545-40ea-85f7-40d07fe2e617", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-592, 96], "webhookId": "1cca6b01-d29b-4f44-95fc-73fa22064cac", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons.", "additionalFields": {}}, "id": "cdee87f6-e787-46e0-a0df-60f59d531834", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1600, -144], "webhookId": "54a07ea0-e42b-49cd-8ad1-57700d90dc54", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}, {"keyName": "role"}]}}, "id": "db05e5d7-3189-4968-912e-d63de7526eb0", "name": "Check if User is Admin1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-544, -368], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}, {"keyName": "role"}]}}, "id": "745039de-7d53-4da4-a387-81e66ae796db", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-704, -512], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{$node['Check if User is Admin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}, {"leftValue": "={{$node['Check if User is Admin'].json[0]?.role}}", "rightValue": "admin", "operation": {"type": "string", "operation": "equal", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "71a5e1dd-908f-42e6-8360-e95d99669238", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-528, -512]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "fc36669c-2e3c-49ce-889b-9e74cc3b8bcb", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-160, -576], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $node[\"Check User in Database\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true", "rightType": "any"}}, {"leftValue": "={{ $node[\"Check User in Database\"].json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "a2940db7-d17b-43ec-ad17-2bc5b8c64d6f", "name": "Check if Verified Investor1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-368, -352]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"Check User in Database\"].json[0].name }}! Your current investment value is: ${{ $node[\"Check User in Database\"].json[0].investment_details.total_value_usd }}.", "additionalFields": {}}, "id": "0c7b844b-b5f3-4327-9b83-f26b53699e76", "name": "Send Investment Details1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-64, -416], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "❌ *Invalid User Data*\n\nSorry, we couldn't process your request due to invalid user information. This might happen if:\n\n• Your user ID is missing or corrupted\n• There's a temporary data issue\n• The bot needs to be restarted\n\n🔄 Please try using /start to restart the bot.\n📞 If the problem persists, contact support.", "additionalFields": {}}, "id": "3f7d66df-3209-46f2-b517-2b2ee0f9413b", "name": "Invalid User E<PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2192, -288], "webhookId": "dcfd838e-0704-44c5-a6f5-3c50e6fac1bf", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "🔧 *Database Connection Error*\n\nWe're experiencing temporary technical difficulties with our database. Please try again in a few moments.\n\n⏳ This is usually resolved quickly\n🔄 Use /start to try again\n📞 Contact support if the issue persists", "additionalFields": {}}, "id": "03b8d7f1-25d8-4c3b-8819-902a6f8e36b9", "name": "Database Error <PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1520, -448], "webhookId": "7f250e6b-0761-43ec-b6a9-3aa81a7d9943", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Test Supabase Connection", "type": "main", "index": 0}]]}, "Standardize User Data": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "Event Router": {"main": [[{"node": "Validate User ID", "type": "main", "index": 0}]]}, "User Exists Check": {"main": [[{"node": "<PERSON> Existing User", "type": "main", "index": 0}], [{"node": "Create New User", "type": "main", "index": 0}]]}, "Create New User": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Database Error <PERSON>", "type": "main", "index": 0}]]}, "Mark New User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], []]}, "Send Welcome": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}]]}, "Build Main Menu": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}]]}, "Mark Existing User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Build Services Menu": {"main": [[{"node": "Send Services Menu", "type": "main", "index": 0}]]}, "Build Donate Menu": {"main": [[{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Check if User is Admin": {"main": [[{"node": "IF Admin", "type": "main", "index": 0}]]}, "IF Admin": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}], [{"node": "Check if Verified Investor1", "type": "main", "index": 0}]]}, "Check if Verified Investor1": {"main": [[{"node": "Send Investment Details1", "type": "main", "index": 0}], [{"node": "Check if Verified Investor", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if User is Admin", "type": "main", "index": 0}]]}, "Send Main Menu": {"main": [[{"node": "Check User in Database", "type": "main", "index": 0}]]}, "Validate User ID": {"main": [[{"node": "Debug User Lookup", "type": "main", "index": 0}]]}, "Check Existing User": {"main": [[{"node": "User Exists Check", "type": "main", "index": 0}]]}, "Debug User Lookup": {"main": [[{"node": "Check Existing User", "type": "main", "index": 0}]]}, "Test Supabase Connection": {"main": [[{"node": "Standardize User Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bad5f2ab-e2ee-4c8d-8d86-e6bc6f60a3c3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "cl97drsgNofoPdxU", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}