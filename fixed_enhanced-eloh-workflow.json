{"name": "ELOH Processing Bot - Merged V2+V3 (Updated v2)", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "7d0f9b59-111d-487d-a1d9-0c1eb4806f92", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1840, 64], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "035c468e-2635-4f89-9db7-bf2b89c1d079", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1648, 64]}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet lastName = (message && message.from && message.from.last_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.last_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.last_name);\n\nlet isCallback = !!callbackQuery;\nlet isNewChatMember = !!chatMember;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    unifiedLastName: lastName,\n    isCallback: isCallback,\n    isNewChatMember: isNewChatMember\n  }\n};"}, "id": "801c849d-44f5-48b0-868f-43652b824f90", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1536, 400]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}]}, "options": {}}, "id": "68874d00-6d8f-4f63-b688-767776836ec4", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1344, 48]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": null, "operation": {"type": "string", "operation": "isNotNull"}}]}, "options": {}}, "id": "b8050479-ce7a-4002-a2e0-4a54f242eed4", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1744, 288]}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}]}}, "id": "55171b37-d556-4bf6-a73b-25e53c3a4d9a", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1072, 128], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "equal"}}]}, "options": {}}, "id": "6ce4b51d-143b-4abf-a392-65818276a216", "name": "User Exists Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1264, -112]}, {"parameters": {"operation": "insert", "tableId": "users", "columns": {"columns": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}, {"keyName": "username", "value": "={{ $json.unifiedUsername || '' }}"}, {"keyName": "name", "value": "={{ ((($json.unifiedFirstName || '') + ' ' + ($json.unifiedLastName || '')).trim()) || 'User' }}"}, {"keyName": "is_verified_investor", "value": false}, {"keyName": "role", "value": "investor"}]}}, "id": "0ee35794-7902-4156-aaa0-1254bff78d92", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-944, -32], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true\n  }\n};"}, "id": "6b5e08ca-cbfd-40aa-a652-a94a9c73eaf4", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-768, -48]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: false\n  }\n};"}, "id": "5a83ac2c-43f6-4ff2-9260-7018f5c096c7", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-944, -176]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "main"}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}]}}, "id": "f862f337-bc69-4bff-a6d7-6026e430c8d5", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-800, 512], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "const user = $json.user;\nconst isAdmin = user?.role === 'admin';\n\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\nconst staticKeyboard = [\n  [\n    { text: '🗺️ Public Roadmap', callback_data: 'show_roadmap_public' },\n    { text: '💰 My Investment', callback_data: 'view_investment' }\n  ],\n  [\n    { text: '🔧 Our Services', callback_data: 'services_menu' },\n    { text: '💝 Donate', callback_data: 'donate' }\n  ],\n  [\n    { text: '🏭 Operations', url: 'https://elohprocessing.site/operations.php' },\n    { text: '📞 Contact', url: 'https://elohprocessing.site/contact.php' }\n  ]\n];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: staticKeyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "5e2efed9-fc7d-4b75-9b14-caf85d1de98d", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-384, -160]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {"messageId": "={{ $json.messageId }}", "method": "={{ $json.messageId ? 'editMessageText' : 'sendMessage' }}"}}, "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, -240], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "76d13f87-a731-4129-b4ea-f78f3348be30", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-368, 32], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "c1553c59-50b9-4669-beb3-911ca7505f49", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-144, 48]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}", "parseMode": "<PERSON><PERSON>", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "17af3c98-5c7a-4491-ae0e-6e2b8ad78abe", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [112, -48], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "d56b9344-19a9-4f8b-894e-5ee5c6277e0b", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-688, 224], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}]}}, "id": "95670edb-5fa0-4857-8570-af63829c4bb6", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-464, 272], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "f839a97a-76a6-4c75-be3e-c0a55a82b754", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 560]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Hello {{ $json[0]?.name || 'Investor' }}! Your current investment value is: ${{ $json[0]?.investment_details?.total_value_usd || '0' }}.", "parseMode": "<PERSON><PERSON>", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "657ee10c-a560-4dc5-9238-ed4133b40527", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [448, 512], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "parseMode": "<PERSON><PERSON>", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "7a9395f8-5cab-4c98-bd00-************", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [688, 640], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst serviceButtons = [\n  [{ text: '💳 Mining Services - $500/mo', callback_data: 'order_mining_500' }],\n  [{ text: '💳 Pool Membership - $200/yr', callback_data: 'order_pool_200' }],\n  [{ text: '💳 Consulting - $150/hr', callback_data: 'order_consulting_150' }],\n  [{ text: '💳 Analysis Report - $99', callback_data: 'order_analysis_99' }]\n];\n\nconst keyboard = [...serviceButtons, [\n  { text: '🏠 Back to Main Menu', callback_data: 'main_menu' }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "4491910e-0010-410a-8aaa-adae175594fd", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-384, 720]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {"messageId": "={{ $json.messageId }}", "method": "={{ $json.messageId ? 'editMessageText' : 'sendMessage' }}"}}, "id": "e1f2g3h4-i5j6-7890-1234-567890abcdef", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, 720], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst donateButtons = [\n  [{ text: '$5 - Support ELOH', callback_data: 'donate_5' }],\n  [{ text: '$10 - Fuel Operations', callback_data: 'donate_10' }],\n  [{ text: '$25 - Expand Infrastructure', callback_data: 'donate_25' }],\n  [{ text: 'Custom Amount', callback_data: 'donate_custom' }]\n];\n\nconst keyboard = [...donateButtons, [\n  { text: '🏠 Back to Main Menu', callback_data: 'main_menu' }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "5e2efed9-fc7d-4b75-9b14-caf85d1de98d-donate", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-384, 880]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {"messageId": "={{ $json.messageId }}", "method": "={{ $json.messageId ? 'editMessageText' : 'sendMessage' }}"}}, "id": "m1n2o3p4-q5r6-7890-1234-567890abcdef", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, 880], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons.", "parseMode": "<PERSON><PERSON>", "additionalFields": {"messageId": "={{ $json.isCallback ? $json.callback_query.message.message_id : undefined }}", "method": "={{ $json.isCallback ? 'editMessageText' : 'sendMessage' }}"}}, "id": "u1v2w3x4-y5z6-7890-1234-567890abcdef", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, -400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "connections": {"Telegram Trigger": [{"node": "Set Variables", "type": "main", "index": 0}], "Set Variables": [{"node": "Standardize User Data", "type": "main", "index": 0}], "Standardize User Data": [{"node": "Event Router", "type": "main", "index": 0}, {"node": "Validate User ID", "type": "main", "index": 0}], "Event Router": [{"node": "Validate User ID", "type": "main", "index": 0}, {"node": "Send Welcome", "type": "main", "index": 1}], "Validate User ID": [{"node": "Check Existing User", "type": "main", "index": 0}, {"node": "Unhandled Input", "type": "main", "index": 1}], "Check Existing User": [{"node": "User Exists Check", "type": "main", "index": 0}], "User Exists Check": [{"node": "Create New User", "type": "main", "index": 0}, {"node": "<PERSON> Existing User", "type": "main", "index": 1}], "Create New User": [{"node": "<PERSON>", "type": "main", "index": 0}], "Mark New User": [{"node": "Main Switch", "type": "main", "index": 0}], "Mark Existing User": [{"node": "Main Switch", "type": "main", "index": 0}], "Main Switch": [{"node": "Build Main Menu", "type": "main", "index": 0}, {"node": "Get Roadmap Data", "type": "main", "index": 1}, {"node": "Check User in Database", "type": "main", "index": 2}, {"node": "Build Services Menu", "type": "main", "index": 3}, {"node": "Build Donate Menu", "type": "main", "index": 4}, {"node": "Unhandled Input", "type": "main", "index": 5}], "Build Main Menu": [{"node": "Send Main Menu", "type": "main", "index": 0}], "Get Roadmap Data": [{"node": "Format Roadmap Text", "type": "main", "index": 0}], "Format Roadmap Text": [{"node": "Send Roadmap", "type": "main", "index": 0}], "Check User in Database": [{"node": "Check if Verified Investor", "type": "main", "index": 0}], "Check if Verified Investor": [{"node": "Send Investment Details", "type": "main", "index": 0}, {"node": "Send Not Verified Message", "type": "main", "index": 1}], "Build Services Menu": [{"node": "Send Services Menu", "type": "main", "index": 0}], "Build Donate Menu": [{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]}}