{"name": "ELOH Processing Bot - Merged V2+V3", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "6f89e99a-36d9-44b7-bda8-7c335c576acf", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1472, -144], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "c4966a0f-c6c7-4eff-9825-febc5c6771c8", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1280, -144]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}]}, "options": {}}, "id": "event-router", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1100, -144]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.message?.from?.id || $json.callback_query?.from?.id }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty"}}]}, "options": {}}, "id": "validate-user-data", "name": "Validate User Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1000, -144]}, {"parameters": {"values": {"number": [{"name": "userId", "value": "={{ $json.message?.from?.id || $json.callback_query?.from?.id }}"}, {"name": "chatId", "value": "={{ $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}"}], "string": [{"name": "username", "value": "={{ $json.message?.from?.username || $json.callback_query?.from?.username || '' }}"}, {"name": "firstName", "value": "={{ $json.message?.from?.first_name || $json.callback_query?.from?.first_name || '' }}"}, {"name": "lastName", "value": "={{ $json.message?.from?.last_name || $json.callback_query?.from?.last_name || '' }}"}, {"name": "displayName", "value": "={{ (($json.message?.from?.first_name || $json.callback_query?.from?.first_name || '') + ' ' + ($json.message?.from?.last_name || $json.callback_query?.from?.last_name || '')).trim() || 'User' }}"}], "boolean": [{"name": "is<PERSON><PERSON>back", "value": "={{ !!$json.callback_query }}"}, {"name": "isMessage", "value": "={{ !!$json.message }}"}]}, "options": {}}, "id": "standardize-data", "name": "Standardize User Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-850, -144]}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $json.chatId }}", "action": "typing", "additionalFields": {}}, "id": "show-typing-indicator", "name": "Show Typing Indicator", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-700, -144], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.userId }}"}]}}, "id": "check-existing-user", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-900, -144], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "equal"}}]}, "options": {}}, "id": "user-exists-check", "name": "User Exists Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-750, -144]}, {"parameters": {"operation": "insert", "tableId": "users", "columns": {"columns": [{"keyName": "telegram_id", "value": "={{ $json.userId }}"}, {"keyName": "username", "value": "={{ $json.username }}"}, {"keyName": "name", "value": "={{ $json.displayName }}"}, {"keyName": "is_verified_investor", "value": false}, {"keyName": "role", "value": "investor"}]}}, "id": "create-new-user", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-600, -200], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true\n  }\n};"}, "id": "mark-new-user", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-450, -200]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: false\n  }\n};"}, "id": "mark-existing-user", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-450, -100]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start", "output": 0}, {"value2": "main", "output": 0}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}]}}, "id": "740926b8-52dd-4232-ba7f-e4a67990593f", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-700, -144], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "const user = $json.user;\nconst isAdmin = user?.role === 'admin';\n\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\nconst staticKeyboard = [\n  [\n    { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n    { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n  ],\n  [\n    { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n    { text: \"💝 Donate\", callback_data: \"donate\" }\n  ],\n  [\n    { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n    { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n  ]\n];\n\nreturn {\n  chatId: $json.chatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: staticKeyboard\n  },\n  parse_mode: \"Markdown\",\n  isCallback: $json.isCallback\n};"}, "id": "dynamic-menu-builder", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, -220]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.isCallback }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "check-message-type", "name": "Check Message Type", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-300, -220]}, {"parameters": {"operation": "editMessageText", "chatId": "={{ $json.chatId }}", "messageId": "={{ $json.callback_query?.message?.message_id }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "edit-main-menu", "name": "Edit Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-150, -270], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-main-menu", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-150, -170], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": [{"keyName": "id"}]}}, "id": "c777d33a-0163-4527-a2a1-7cf92c31a980", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-500, -50], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "82aa6a7d-1d10-4d7f-bfac-99ac26f7e7c3", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-300, -50]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "73cb6ff2-ccf4-4f7d-a87d-00b85cbc1aa1", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-100, -50], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "=Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "a692b5e5-1442-4e6c-b7db-7ddc921942a5", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-500, 150], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.message?.from?.id || $json.callback_query?.from?.id }}"}]}}, "id": "********-89c2-4a65-b29a-f01e29e0684f", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-500, 50], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "8aa01e94-a179-410a-8aaa-adae175594fd", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-300, 50]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "=Hello {{ $json[0]?.name || 'Investor' }}! Your current investment value is: ${{ $json[0]?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "5e32643e-66a0-4a32-88c9-bfedcff5b529", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-100, 0], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "2268e8e8-aaf3-4c69-90ac-30db9546dcf7", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-100, 100], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst serviceButtons = [\n  [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n  [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n  [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n  [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n];\n\nconst keyboard = [...serviceButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.chatId,\n  text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  isCallback: $json.isCallback,\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "service-menu-builder", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, 300]}, {"parameters": {"operation": "editMessageText", "chatId": "={{ $json.chatId }}", "messageId": "={{ $json.messageId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-services-menu", "name": "Edit Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-300, 300], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const donationKeyboard = [\n  [{ text: \"🌱 Donate $100\", url: \"https://elohprocessing.site/multi-gateway-payment-form.php?type=donation&amount=100\" }],\n  [{ text: \"🌿 Donate $1,000\", url: \"https://elohprocessing.site/multi-gateway-payment-form.php?type=donation&amount=1000\" }],\n  [{ text: \"💎 Custom Amount\", url: \"https://elohprocessing.site/multi-gateway-payment-form.php?type=donation\" }],\n  [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n];\n\nreturn {\n  chatId: $json.chatId,\n  text: `💝 **Support Our Mission**\n\nYour contribution directly funds our expansion of renewable-powered mining operations in Dominica.\n\n🌱 **Donation Tiers:**\n• **Supporter**: $50+ - Renewable energy research\n• **Advocate**: $500+ - Equipment upgrades  \n• **Champion**: $2,500+ - Major renewable impact\n• **Strategic**: $10,000+ - 10% equity opportunity\n\nEvery donation helps build a more sustainable crypto future!`,\n  reply_markup: {\n    inline_keyboard: donationKeyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "donation-handler", "name": "Handle Donation Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, 400]}, {"parameters": {"operation": "editMessageText", "chatId": "={{ $json.chatId }}", "messageId": "={{ $json.messageId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-donation-menu", "name": "Edit Donation Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-300, 400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const callbackData = $json.callback_query.data.split('_');\nconst serviceId = callbackData[1];\nconst price = parseFloat(callbackData[2]);\n\nconst services = {\n  'mining': { name: 'Crypto Mining Services', description: '24/7 sustainable mining', price: 500 },\n  'pool': { name: 'Mining Pool Membership', description: 'Join our transparent pool', price: 200 },\n  'consulting': { name: 'Strategy Consulting', description: 'Expert guidance', price: 150 },\n  'analysis': { name: 'Market Analysis Report', description: 'Detailed insights', price: 99 }\n};\nconst serviceData = services[serviceId] || { name: 'Service', description: 'ELOH Processing Service', price: price };\n\nreturn {\n  chatId: $json.callback_query.message.chat.id,\n  title: serviceData.name,\n  description: serviceData.description || 'ELOH Processing Service',\n  payload: `eloh_service_${serviceId}_${$json.user?.user_id || 'user'}_${Date.now()}`,\n  currency: 'USD',\n  prices: [{\n    label: serviceData.name,\n    amount: Math.round(serviceData.price * 100)\n  }],\n  photo_url: 'https://elohprocessing.site/assets/img/logo-e-p.png',\n  need_email: true,\n  need_name: true\n};"}, "id": "invoice-builder", "name": "Prepare Invoice", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, 500]}, {"parameters": {"operation": "sendInvoice", "chatId": "={{ $json.chatId }}", "title": "={{ $json.title }}", "description": "={{ $json.description }}", "payload": "={{ $json.payload }}", "currency": "={{ $json.currency }}", "prices": "={{ $json.prices }}", "additionalFields": {"photo_url": "={{ $json.photo_url }}", "need_name": "={{ $json.need_name }}", "need_email": "={{ $json.need_email }}", "send_email_to_provider": true}}, "id": "send-invoice", "name": "Send Invoice", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-300, 500], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "answer", "preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}", "ok": true, "additionalFields": {}}, "id": "pre-checkout-handler", "name": "Confirm Pre-Checkout", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-900, 200], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const payment = $json.message.successful_payment;\nconst user = $json.user;\n\nconst [prefix, serviceId, userId, timestamp] = payment.invoice_payload.split('_');\n\nreturn {\n  chatId: payment.chat.id,\n  text: `✅ **Payment Successful!**\n\n🎉 **Order Confirmed:**\n• Service: ${serviceId}\n• Amount: $${payment.total_amount / 100} USD\n• Email: ${payment.order_info?.email || 'N/A'}\n• Order ID: ${timestamp}\n\nThank you for your payment! You'll receive access details shortly.`,\n  parse_mode: 'Markdown'\n};"}, "id": "payment-success-handler", "name": "Handle Payment Success", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-900, 400]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "send-success-message", "name": "Send Success Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-700, 400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Centralized error handler\nconst errorMessage = $json.error?.message || $json.message || 'Something went wrong';\nconst chatId = $json.chatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id;\n\nreturn {\n  chatId: chatId,\n  text: `❌ **Error**\\n\\nSorry, something went wrong. Please try again later.\\n\\n*Technical details:* ${errorMessage}`,\n  parse_mode: 'Markdown'\n};"}, "id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 200]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "send-error-message", "name": "Send Error Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1000, 200], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "⚠️ **Invalid Request**\\n\\nThis action requires a valid user context. Please start a conversation with the bot first by sending /start.", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "send-invalid-user-message", "name": "Send Invalid User Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-850, 200], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "event-router", "type": "main", "index": 0}]]}, "event-router": {"main": [[{"node": "validate-user-data", "type": "main", "index": 0}], [{"node": "pre-checkout-handler", "type": "main", "index": 0}], [{"node": "payment-success-handler", "type": "main", "index": 0}]]}, "validate-user-data": {"main": [[{"node": "standardize-data", "type": "main", "index": 0}], [{"node": "send-invalid-user-message", "type": "main", "index": 0}]]}, "standardize-data": {"main": [[{"node": "show-typing-indicator", "type": "main", "index": 0}]]}, "show-typing-indicator": {"main": [[{"node": "check-existing-user", "type": "main", "index": 0}]]}, "check-existing-user": {"main": [[{"node": "user-exists-check", "type": "main", "index": 0}]]}, "user-exists-check": {"main": [[{"node": "create-new-user", "type": "main", "index": 0}], [{"node": "mark-existing-user", "type": "main", "index": 0}]]}, "create-new-user": {"main": [[{"node": "mark-new-user", "type": "main", "index": 0}]]}, "mark-new-user": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "mark-existing-user": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "dynamic-menu-builder", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "service-menu-builder", "type": "main", "index": 0}], [{"node": "donation-handler", "type": "main", "index": 0}], [{"node": "invoice-builder", "type": "main", "index": 0}]]}, "dynamic-menu-builder": {"main": [[{"node": "check-message-type", "type": "main", "index": 0}]]}, "check-message-type": {"main": [[{"node": "edit-main-menu", "type": "main", "index": 0}], [{"node": "send-main-menu", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if Verified Investor", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "service-menu-builder": {"main": [[{"node": "send-services-menu", "type": "main", "index": 0}]]}, "donation-handler": {"main": [[{"node": "send-donation-menu", "type": "main", "index": 0}]]}, "invoice-builder": {"main": [[{"node": "send-invoice", "type": "main", "index": 0}]]}, "payment-success-handler": {"main": [[{"node": "send-success-message", "type": "main", "index": 0}]]}, "error-handler": {"main": [[{"node": "send-error-message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "merged-v2-v3-enhanced", "meta": {"templateCredsSetupCompleted": true, "instanceId": "eloh-processing-bot-merged"}, "id": "merged-workflow-id", "tags": [{"createdAt": "2025-08-08T00:00:00.000Z", "updatedAt": "2025-08-08T00:00:00.000Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}