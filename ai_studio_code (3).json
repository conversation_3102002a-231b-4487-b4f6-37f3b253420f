{"name": "ELOH Processing Bot - Unified System", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1800, -40], "webhookId": "unified-webhook-id", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "set-variables", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1620, -40]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "pre_checkout_condition", "leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "exists"}}, {"id": "successful_payment_condition", "leftValue": "={{ $json.message.successful_payment }}", "rightValue": "", "operation": {"type": "exists"}, "output": 2}, {"id": "new_member_condition", "leftValue": "={{ $json.chat_member }}", "rightValue": "", "operation": {"type": "exists"}, "output": 3}]}, "options": {}}, "id": "event-router", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1440, -40]}, {"parameters": {"jsCode": "const telegramId = $json.message?.from?.id || $json.callback_query?.from?.id;\n\n// Exit if no user ID (e.g., channel post)\nif (!telegramId) {\n  return null;\n}\n\n// Get Supabase client from credentials\nconst supabase = this.getCredentials('supabaseApi');\nif (!supabase) {\n  throw new Error('Supabase credentials not configured');\n}\n\n// Bootstrap data if empty\nconst { data: vendorCount } = await supabase.from('vendors').select('id', { count: 'exact' });\n\nif (vendorCount === 0) {\n  // Populate initial data\n  await supabase.from('vendors').upsert({\n    id: 'eloh-processing-llc',\n    name: 'ELOH Processing LLC',\n    description: 'Sustainable crypto mining in Dominica',\n    contact_info: {\n      email: '<EMAIL>',\n      phone: '******-555-0123',\n      location: 'Dominica, Caribbean'\n    },\n    website_url: 'https://elohprocessing.site',\n    image_url: 'https://elohprocessing.site/assets/img/logo-e-p.png'\n  });\n\n  await supabase.from('menu_items').upsert([\n    {\n      item_id: 'mining-service',\n      name: 'Crypto Mining Services',\n      description: '24/7 sustainable ASIC mining with renewable energy',\n      price: 500.00,\n      category: 'services',\n      image_url: 'https://elohprocessing.site/assets/mining.jpg',\n      is_available: true\n    },\n    {\n      item_id: 'pool-membership',\n      name: 'Mining Pool Membership',\n      description: 'Join our transparent mining pool with low fees',\n      price: 200.00,\n      category: 'membership',\n      image_url: 'https://elohprocessing.site/assets/pool.jpg',\n      is_available: true\n    },\n    {\n      item_id: 'consulting-service',\n      name: 'Crypto & Forex Consulting',\n      description: 'Expert consultation on crypto and forex strategies',\n      price: 150.00,\n      category: 'consulting',\n      image_url: 'https://elohprocessing.site/assets/consulting.jpg',\n      is_available: true\n    },\n    {\n      item_id: 'market-analysis',\n      name: 'Market Analysis Report',\n      description: 'Detailed weekly market analysis and insights',\n      price: 99.00,\n      category: 'analysis',\n      image_url: 'https://elohprocessing.site/assets/analysis.jpg',\n      is_available: true\n    }\n  ]);\n\n  await supabase.from('bot_menu').upsert([\n    {\n      menu_type: 'main',\n      button_text: '🗺️ Public Roadmap',\n      button_type: 'callback',\n      button_data: 'show_roadmap',\n      description: 'Display current development roadmap',\n      is_active: true\n    },\n    {\n      menu_type: 'main',\n      button_text: '💰 My Investment',\n      button_type: 'callback',\n      button_data: 'view_investment',\n      description: 'View your investment portfolio',\n      requires_verification: true,\n      is_active: true\n    },\n    {\n      menu_type: 'main',\n      button_text: '🔧 Services',\n      button_type: 'callback',\n      button_data: 'show_services',\n      description: 'Browse available services',\n      is_active: true\n    }\n  ]);\n\n  await supabase.from('project_roadmap').upsert([\n    { id: 'infrastructure', name: 'Infrastructure Setup', status: 'Complete', details: 'Mining facility established' },\n    { id: 'mining-ops', name: 'Mining Operations', status: 'Active', details: '150 TH/s total hash rate' },\n    { id: 'renewable-energy', name: '100% Renewable Energy', status: 'In Progress', details: 'Target completion by 2026' },\n    { id: 'expansion', name: 'Caribbean Expansion', status: 'Planned', details: 'Regional growth strategy' }\n  ]);\n}\n\n// Check for existing user\nconst { data: existingUser, error: selectError } = await $supabase\n  .from('users')\n  .select('*')\n  .eq('telegram_id', telegramId)\n  .single();\n\nif (!existingUser && !selectError) {\n  // Create new user\n  const telegramData = {\n    username: $json.message?.from?.username || $json.callback_query?.from?.username,\n    first_name: $json.message?.from?.first_name || $json.callback_query?.from?.first_name,\n    last_name: $json.message?.from?.last_name || $json.callback_query?.from?.last_name,\n    language_code: $json.message?.from?.language_code || 'en'\n  };\n\n  const { data: newUser, error: insertError } = await $supabase\n    .from('users')\n    .insert({\n      telegram_id: telegramId,\n      telegram_username: telegramData.username,\n      name: `${telegramData.first_name || ''} ${telegramData.last_name || ''}`.trim() || 'User',\n      telegram_first_name: telegramData.first_name,\n      telegram_last_name: telegramData.last_name,\n      telegram_language_code: telegramData.language_code,\n      is_bot_menu_user: true,\n      signup_completed_at: new Date().toISOString()\n    })\n    .select()\n    .single();\n\n  if (insertError) {\n    throw new Error(`Failed to create user: ${insertError.message}`);\n  }\n\n  return [{\n    json: {\n      ...$input.first().json,\n      user: newUser,\n      is_new_user: true\n    }\n  }];\n} else {\n  if (selectError && selectError.code !== 'PGRST116') {\n    throw new Error(`Database error: ${selectError.message}`);\n  }\n  \n  return [{\n    json: {\n      ...$input.first().json,\n      user: existingUser,\n      is_new_user: false\n    }\n  }];\n}"}, "id": "user-signup-flow", "name": "Check or Create User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1260, -40], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : 'start') }}", "rules": {"rules": [{"value2": "/start", "output": 0}, {"value2": "main", "output": 0}, {"value2": "start", "output": 0}, {"value2": "services", "output": 1}, {"value2": "order", "output": 2}, {"value2": "info", "output": 3}, {"value2": "show", "output": 4}, {"value2": "view", "output": 5}, {"value2": "donate", "output": 6}, {"value2": "/kick", "output": 7}, {"value2": "/pin", "output": 7}, {"value2": "/verify", "output": 7}]}}, "id": "main-menu-switch", "name": "Main Menu Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1080, -40]}, {"parameters": {"jsCode": "const user = $json.user;\nconst isAdmin = user?.role === 'admin';\n\n// Try to get dynamic menu from database first\ntry {\n  const { data: menus, error } = await $supabase\n    .from('bot_menu')\n    .select('*')\n    .eq('is_active', true)\n    .order('order_index', { ascending: true });\n\n  if (!error && menus && menus.length > 0) {\n    const filteredMenus = menus.filter(menu => \n      (!menu.requires_verification || user.is_verified_investor) &&\n      (!menu.admin_only || isAdmin)\n    );\n\n    const keyboard = [];\n    let currentRow = [];\n\n    for (const menu of filteredMenus) {\n      const button = { text: menu.button_text };\n      if (menu.button_type === 'url') {\n        button.url = menu.button_data;\n      } else {\n        button.callback_data = menu.button_data;\n      }\n      \n      currentRow.push(button);\n      \n      if (currentRow.length === 2 || menu.button_type === 'url') {\n        keyboard.push([...currentRow]);\n        currentRow = [];\n      }\n    }\n    if (currentRow.length > 0) {\n      keyboard.push([...currentRow]);\n    }\n\n    const welcomeText = $json.is_new_user \n      ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n      : `🎉 Welcome back, ${user?.name || 'User'}!`;\n\n    const userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\n    return {\n      chatId: $json.message?.chat?.id || $json.callback_query?.message?.chat?.id,\n      text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n      reply_markup: {\n        inline_keyboard: keyboard\n      },\n      parse_mode: \"Markdown\"\n    };\n  }\n} catch (dbError) {\n  console.log('Database menu failed, using fallback static menu');\n}\n\n// Fallback to static menu with website integration\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\nconst staticKeyboard = [\n  [\n    { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap\" },\n    { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n  ],\n  [\n    { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n    { text: \"💝 Donate\", callback_data: \"donate_menu\" }\n  ],\n  [\n    { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n    { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n  ]\n];\n\nreturn {\n  chatId: $json.message?.chat?.id || $json.callback_query?.message?.chat?.id,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: staticKeyboard\n  },\n  parse_mode: \"Markdown\"\n};"}, "id": "dynamic-menu-builder", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, -220], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-main-menu", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, -220], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\n// Try to get services from database\ntry {\n  const { data: services, error } = await $supabase\n    .from('menu_items')\n    .select('*')\n    .eq('is_available', true)\n    .order('order_index');\n\n  let serviceButtons = [];\n\n  if (!error && services && services.length > 0) {\n    serviceButtons = services.map(service => [{\n      text: `${service.name} - $${service.price}`,\n      callback_data: `order_${service.item_id}_${service.price}`\n    }]);\n  } else {\n    // Fallback services from website\n    serviceButtons = [\n      [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n      [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n      [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n      [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n    ];\n  }\n\n  const keyboard = [...serviceButtons, [\n    { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n  ]];\n\n  return {\n    chatId: $json.callback_query.message.chat.id,\n    text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n    reply_markup: {\n      inline_keyboard: keyboard\n    },\n    parse_mode: 'Markdown'\n  };\n} catch (error) {\n  return {\n    chatId: $json.callback_query.message.chat.id,\n    text: `🔧 *ELOH Processing Services*\\n\\nOur services are temporarily unavailable. Please try again later.\\n\\n• **Mining Services**: $500/month\\n• **Pool Membership**: $200/year\\n• **Consulting**: $150/hour\\n• **Analysis Reports**: $99/report`,\n    reply_markup: {\n      inline_keyboard: [[\n        { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n      ]]\n    },\n    parse_mode: 'Markdown'\n  };\n}"}, "id": "service-menu-builder", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, -40], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.callback_query.message.chat.id }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-services-menu", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, -40], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const callbackData = $json.callback_query.data.split('_');\nconst serviceId = callbackData[1];\nconst price = parseFloat(callbackData[2]);\n\n// Try to get service from database\ntry {\n  const { data: service, error } = await $supabase\n    .from('menu_items')\n    .select('*')\n    .eq('item_id', serviceId)\n    .single();\n\n  let serviceData = service;\n\n  if (error || !service) {\n    // Fallback service data\n    const services = {\n      'mining': { name: 'Crypto Mining Services', description: '24/7 sustainable mining', price: 500 },\n      'pool': { name: 'Mining Pool Membership', description: 'Join our transparent pool', price: 200 },\n      'consulting': { name: 'Strategy Consulting', description: 'Expert guidance', price: 150 },\n      'analysis': { name: 'Market Analysis Report', description: 'Detailed insights', price: 99 }\n    };\n    serviceData = services[serviceId] || { name: 'Service', description: 'ELOH Processing Service', price: price };\n  }\n\n  return {\n    chatId: $json.callback_query.message.chat.id,\n    title: serviceData.name,\n    description: serviceData.description || 'ELOH Processing Service',\n    payload: `eloh_service_${serviceId}_${$json.user.user_id}_${Date.now()}`,\n    currency: 'USD',\n    prices: [{\n      label: serviceData.name,\n      amount: Math.round(serviceData.price * 100)\n    }],\n    photo_url: serviceData.image_url || 'https://elohprocessing.site/assets/img/logo-e-p.png',\n    need_email: true,\n    need_name: true\n  };\n} catch (error) {\n  return {\n    chatId: $json.callback_query.message.chat.id,\n    title: 'ELOH Processing Service',\n    description: 'Premium service from ELOH Processing',\n    payload: `eloh_service_${serviceId}_${$json.user.user_id}_${Date.now()}`,\n    currency: 'USD',\n    prices: [{\n      label: 'Service',\n      amount: Math.round(price * 100)\n    }],\n    photo_url: 'https://elohprocessing.site/assets/img/logo-e-p.png',\n    need_email: true,\n    need_name: true\n  };\n}"}, "id": "invoice-builder", "name": "Prepare Invoice", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 140], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "title": "={{ $json.title }}", "description": "={{ $json.description }}", "payload": "={{ $json.payload }}", "providerToken": "{{ $credentials.telegramPaymentProvider.token }}", "currency": "={{ $json.currency }}", "prices": "={{ $json.prices }}", "additionalFields": {"photo_url": "={{ $json.photo_url }}", "need_name": "={{ $json.need_name }}", "need_email": "={{ $json.need_email }}", "send_email_to_provider": true}}, "id": "send-invoice", "name": "Send Invoice", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 140], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const callbackData = $json.callback_query?.data || '';\n\n// Handle roadmap display\nif (callbackData === 'show_roadmap') {\n  try {\n    const { data: roadmapData, error } = await $supabase\n      .from('project_roadmap')\n      .select('*')\n      .order('id');\n\n    if (error) {\n      throw new Error(`Roadmap fetch error: ${error.message}`);\n    }\n\n    let formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\n    if (roadmapData && roadmapData.length > 0) {\n      for (const item of roadmapData) {\n        const icon = item.status === 'Complete' ? '✅' : \n                     item.status === 'Active' ? '🟡' : '⏳';\n        formattedText += `${icon} **${item.name}**: ${item.status}\\n`;\n      }\n    } else {\n      formattedText += '✅ **Phase 1: Infrastructure Setup**: Complete\\n';\n      formattedText += '🟡 **Phase 2: Mining Operations**: 150 TH/s Active\\n';\n      formattedText += '⏳ **Phase 3: Renewable Energy**: 100% by 2026\\n';\n      formattedText += '⏳ **Phase 4: Caribbean Expansion**: Planned';\n    }\n\n    return {\n      messageToSend: {\n        text: formattedText,\n        chatId: $json.callback_query.message.chat.id,\n        parse_mode: 'Markdown'\n      }\n    };\n  } catch (error) {\n    return {\n      messageToSend: {\n        text: '🗺️ **ELOH Processing Roadmap**\\n\\n✅ **Infrastructure Setup**: Complete\\n🟡 **Mining Operations**: 150 TH/s Active\\n⏳ **Renewable Energy**: 100% by 2026\\n⏳ **Caribbean Expansion**: Planned',\n        chatId: $json.callback_query.message.chat.id,\n        parse_mode: 'Markdown'\n      }\n    };\n  }\n}\n\n// Handle other info responses\ntry {\n  const { data: response, error } = await $supabase\n    .from('menu_responses')\n    .select('*')\n    .eq('callback_data', callbackData)\n    .eq('is_active', true)\n    .single();\n\n  if (response) {\n    return {\n      messageToSend: {\n        text: response.response_text || 'Information not available',\n        chatId: $json.callback_query.message.chat.id,\n        parse_mode: 'Markdown'\n      }\n    };\n  }\n} catch (dbError) {\n  // Continue to fallback\n}\n\n// Fallback responses\nconst fallbackResponses = {\n  'show_roadmap': '🗺️ **ELOH Processing Roadmap**\\n\\n✅ **Infrastructure Setup**: Complete\\n🟡 **Mining Operations**: 150 TH/s Active\\n⏳ **Renewable Energy**: 100% by 2026\\n⏳ **Caribbean Expansion**: Planned',\n  'show_services': '🔧 **Available Services**\\n\\n💳 **Mining Services**: $500/month\\n💳 **Pool Membership**: $200/year\\n💳 **Consulting**: $150/hour\\n💳 **Analysis Reports**: $99/report',\n  'donate_menu': '💝 **Support Our Mission**\\n\\n🌱 Donations help fund renewable energy initiatives in Dominica.',\n  'default': '🤖 How can I help you today?'\n};\n\nreturn {\n  messageToSend: {\n    text: fallbackResponses[callbackData] || fallbackResponses.default,\n    chatId: $json.callback_query.message.chat.id,\n    parse_mode: 'Markdown'\n  }\n};"}, "id": "info-response-handler", "name": "Handle Info Requests", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 320], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.messageToSend.chatId }}", "text": "={{ $json.messageToSend.text }}", "parseMode": "={{ $json.messageToSend.parse_mode }}", "additionalFields": {}}, "id": "send-info-response", "name": "Send Info Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 320], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\n// Check if user is verified investor\nif (!user.is_verified_investor) {\n  return {\n    messageToSend: {\n      text: 'Your Telegram account is not yet linked to a verified investor profile. Please contact support.',\n      chatId: $json.callback_query ? $json.callback_query.message.chat.id : $json.message.chat.id,\n      parse_mode: 'Markdown'\n    }\n  };\n}\n\n// Display investment information\nconst investmentValue = user.investment_details?.total_value_usd || 0;\nreturn {\n  messageToSend: {\n    text: `💰 **Investment Portfolio**\n\nHello ${user.name}!\n\n📊 Current Investment Value: $${investmentValue}\n💎 Status: Verified Investor\n\n_For detailed portfolio information, please visit our investor portal._`,\n    chatId: $json.callback_query ? $json.callback_query.message.chat.id : $json.message.chat.id,\n    parse_mode: 'Markdown'\n  }\n};"}, "id": "investment-handler", "name": "Handle Investment View", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 500], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.messageToSend.chatId }}", "text": "={{ $json.messageToSend.text }}", "parseMode": "={{ $json.messageToSend.parse_mode }}", "additionalFields": {}}, "id": "send-investment-info", "name": "Send Investment Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 500], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const donationKeyboard = [\n  [{ text: \"🌱 Donate $100\", url: \"https://elohprocessing.site/multi-gateway-payment-form.php?type=donation&amount=100\" }],\n  [{ text: \"🌿 Donate $1,000\", url: \"https://elohprocessing.site/multi-gateway-payment-form.php?type=donation&amount=1000\" }],\n  [{ text: \"💎 Custom Amount\", url: \"https://elohprocessing.site/multi-gateway-payment-form.php?type=donation\" }],\n  [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n];\n\nreturn {\n  chatId: $json.callback_query.message.chat.id,\n  text: `💝 **Support Our Mission**\n\nYour contribution directly funds our expansion of renewable-powered mining operations in Dominica.\n\n🌱 **Donation Tiers:**\n• **Supporter**: $50+ - Renewable energy research\n• **Advocate**: $500+ - Equipment upgrades  \n• **Champion**: $2,500+ - Major renewable impact\n• **Strategic**: $10,000+ - 10% equity opportunity\n\nEvery donation helps build a more sustainable crypto future!`,\n  reply_markup: {\n    inline_keyboard: donationKeyboard\n  },\n  parse_mode: 'Markdown'\n};"}, "id": "donation-handler", "name": "Handle Donation Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 680]}, {"parameters": {"chatId": "={{ $json.chatId || $json.callback_query.message.chat.id }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-donation-menu", "name": "Send Donation Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 680]}, {"parameters": {"operation": "answer", "preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}", "ok": true, "additionalFields": {}}, "id": "pre-checkout-handler", "name": "Confirm Pre-Checkout", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1080, 140], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const payment = $json.message.successful_payment;\nconst user = $json.user;\n\n// Parse payload\nconst [prefix, serviceId, userId, timestamp] = payment.invoice_payload.split('_');\n\n// Get service info\ntry {\n  const { data: service, error } = await $supabase\n    .from('menu_items')\n    .select('*')\n    .eq('item_id', serviceId)\n    .single();\n\n  let serviceData = service || { name: 'Service', price: payment.total_amount / 100 };\n\n  // Create order\n  const order = {\n    user_id: userId,\n    telegram_user_id: payment.chat.id,\n    telegram_username: user.telegram_username,\n    chat_id: payment.chat.id,\n    items: [{\n      service_id: serviceId,\n      name: serviceData.name,\n      price: payment.total_amount / 100\n    }],\n    total_amount: payment.total_amount / 100,\n    currency: payment.currency,\n    payment_status: \"paid\",\n    payment_payload: payment.invoice_payload,\n    telegram_charge_id: payment.telegram_payment_charge_id,\n    provider_charge_id: payment.provider_payment_charge_id,\n    order_status: \"confirmed\",\n    created_at: new Date().toISOString()\n  };\n\n  await $supabase.from('orders').insert(order);\n\n  return {\n    chatId: payment.chat.id,\n    text: `✅ **Payment Successful!**\n\n🎉 **Order Confirmed:**\n• Service: ${serviceData.name}\n• Amount: $${payment.total_amount / 100} USD\n• Email: ${payment.order_info.email}\n• Order ID: ${timestamp}\n\nThank you for your payment! You'll receive access details shortly.`,\n    parse_mode: 'Markdown'\n  };\n} catch (error) {\n  return {\n    chatId: payment.chat.id,\n    text: `✅ **Payment Successful!**\n\n🎉 Thank you for your payment of $${payment.total_amount / 100} USD!\\n\\nYou'll receive access details at ${payment.order_info.email}`,\n    parse_mode: 'Markdown'\n  };\n}"}, "id": "payment-success-handler", "name": "Handle Payment Success", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1080, 320], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "send-success-message", "name": "Send Success Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-880, 320], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "🎉 Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! \n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['set-variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "send-welcome", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1080, 500], "webhookId": "welcome-webhook-id", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\nconst messageText = $json.message?.text || '';\nconst chatId = $json.message?.chat?.id;\n\n// 1. Check if the user is an admin\nif (user?.role !== 'admin') {\n  return [{\n    json: {\n      chatId: chatId,\n      text: \"❌ **Access Denied**\\n\\nYou do not have permission to use this command.\",\n      parse_mode: 'Markdown'\n    }\n  }];\n}\n\nconst [command, targetArgument] = messageText.split(' ');\n\n// 2. Implement the /verify command\nif (command === '/verify' && targetArgument) {\n  const targetUsername = targetArgument.replace('@', '');\n\n  try {\n    // Find the user to verify by their username\n    const { data: userToVerify, error: findError } = await $supabase\n      .from('users')\n      .select('id, is_verified_investor')\n      .eq('telegram_username', targetUsername)\n      .single();\n\n    if (findError) throw new Error(`Could not find user @${targetUsername}.`);\n    if (userToVerify.is_verified_investor) {\n       return [{\n          json: {\n            chatId: chatId,\n            text: `⚠️ **Already Verified**\\n\\nUser @${targetUsername} is already a verified investor.`,\n            parse_mode: 'Markdown'\n          }\n       }];\n    }\n\n    // Update the user to set them as verified\n    const { error: updateError } = await $supabase\n      .from('users')\n      .update({\n        is_verified_investor: true,\n        is_verified_at: new Date().toISOString()\n      })\n      .eq('id', userToVerify.id);\n\n    if (updateError) throw new Error(`Database error during verification: ${updateError.message}`);\n\n    // Return success message\n    return [{\n      json: {\n        chatId: chatId,\n        text: `✅ **User Verified**\\n\\nSuccessfully verified @${targetUsername} as an investor.`,\n        parse_mode: 'Markdown'\n      }\n    }];\n\n  } catch (error) {\n    return [{\n      json: {\n        chatId: chatId,\n        text: `❌ **Error**\\n\\n${error.message}`,\n        parse_mode: 'Markdown'\n      }\n    }];\n  }\n}\n\n// 3. Add a placeholder for other admin commands\nif (command === '/kick' || command === '/pin') {\n    return [{\n      json: {\n        chatId: chatId,\n        text: `⚙️ The ${command} command is not fully implemented yet.`,\n        parse_mode: 'Markdown'\n      }\n    }];\n}\n\n\n// Default response if the admin command is not recognized\nreturn [{\n  json: {\n    chatId: chatId,\n    text: `🤔 Unknown admin command. Try '/verify @username'.`,\n    parse_mode: 'Markdown'\n  }\n}];"}, "id": "admin-action-handler", "name": "Handle Admin Actions", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 860], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "parseMode": "={{ $json.parse_mode }}", "additionalFields": {}}, "id": "send-admin-response", "name": "Send Admin Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 860], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "connections": {"telegram-trigger": {"main": [[{"node": "set-variables", "type": "main", "index": 0}]]}, "set-variables": {"main": [[{"node": "event-router", "type": "main", "index": 0}]]}, "event-router": {"main": [[{"node": "user-signup-flow", "type": "main", "index": 0}], [{"node": "pre-checkout-handler", "type": "main", "index": 0}], [{"node": "payment-success-handler", "type": "main", "index": 0}], [{"node": "send-welcome", "type": "main", "index": 0}]]}, "user-signup-flow": {"main": [[{"node": "main-menu-switch", "type": "main", "index": 0}]]}, "main-menu-switch": {"main": [[{"node": "dynamic-menu-builder", "type": "main", "index": 0}], [{"node": "service-menu-builder", "type": "main", "index": 0}], [{"node": "invoice-builder", "type": "main", "index": 0}], [{"node": "info-response-handler", "type": "main", "index": 0}], [{"node": "info-response-handler", "type": "main", "index": 0}], [{"node": "investment-handler", "type": "main", "index": 0}], [{"node": "donation-handler", "type": "main", "index": 0}], [{"node": "admin-action-handler", "type": "main", "index": 0}]]}, "dynamic-menu-builder": {"main": [[{"node": "send-main-menu", "type": "main", "index": 0}]]}, "service-menu-builder": {"main": [[{"node": "send-services-menu", "type": "main", "index": 0}]]}, "invoice-builder": {"main": [[{"node": "send-invoice", "type": "main", "index": 0}]]}, "info-response-handler": {"main": [[{"node": "send-info-response", "type": "main", "index": 0}]]}, "investment-handler": {"main": [[{"node": "send-investment-info", "type": "main", "index": 0}]]}, "donation-handler": {"main": [[{"node": "send-donation-menu", "type": "main", "index": 0}]]}, "payment-success-handler": {"main": [[{"node": "send-success-message", "type": "main", "index": 0}]]}, "admin-action-handler": {"main": [[{"node": "send-admin-response", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "production-ready-v3-connections-fixed", "meta": {"templateCredsSetupCompleted": true, "instanceId": "eloh-processing-bot"}}