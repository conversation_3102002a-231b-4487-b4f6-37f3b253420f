{"name": "My workflow copy", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "7d0f9b59-111d-487d-a1d9-0c1eb4806f92", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1840, 64], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "035c468e-2635-4f89-9db7-bf2b89c1d079", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1648, 64]}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet lastName = (message && message.from && message.from.last_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.last_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.last_name);\n\nlet isCallback = !!callbackQuery;\nlet isNewChatMember = !!chatMember;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    unifiedLastName: lastName,\n    isCallback: isCallback,\n    isNewChatMember: isNewChatMember\n  }\n};"}, "id": "801c849d-44f5-48b0-868f-43652b824f90", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1536, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}], "combineOperation": "any"}, "options": {}}, "id": "68874d00-6d8f-4f63-b688-767776836ec4", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1344, 48]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ typeof $json.unifiedUserId }}", "rightValue": "undefined", "operation": {"type": "string", "operation": "notEqual", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "b8050479-ce7a-4002-a2e0-4a54f242eed4", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1744, 288]}, {"parameters": {"jsCode": "// Debug: Check what data is being passed to user lookup\nconsole.log('Data before user lookup:', JSON.stringify($json, null, 2));\nconsole.log('Unified User ID:', $json.unifiedUserId);\nconsole.log('Type of User ID:', typeof $json.unifiedUserId);\n\nreturn $input.all();"}, "id": "debug-before-lookup", "name": "Debug Before User Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 128]}, {"parameters": {"jsCode": "// Test Supabase connection by checking if we can reach this point\nconsole.log('=== ABOUT TO QUERY SUPABASE ===');\nconsole.log('User ID to search:', $json.unifiedUserId);\nconsole.log('User ID type:', typeof $json.unifiedUserId);\nconsole.log('Full data object:', JSON.stringify($json, null, 2));\nreturn $input.all();"}, "id": "test-supabase-connection", "name": "Test Supabase Connection", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 0]}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}]}, "options": {}}, "id": "55171b37-d556-4bf6-a73b-25e53c3a4d9a", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1072, 128], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Debug: Check what data we got from user lookup\nconsole.log('=== SUPABASE QUERY RESULT ===');\nconsole.log('Raw response:', JSON.stringify($json, null, 2));\nconsole.log('Response type:', typeof $json);\nconsole.log('Is array:', Array.isArray($json));\nif (Array.isArray($json)) {\n  console.log('Array length:', $json.length);\n  console.log('First item:', $json[0]);\n} else {\n  console.log('Not an array - checking properties:', Object.keys($json));\n}\nconsole.log('=== END SUPABASE RESULT ===');\n\nreturn $input.all();"}, "id": "debug-user-lookup", "name": "Debug User Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1264, -200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "equal", "rightType": "any"}}], "combineOperation": "any"}, "options": {}}, "id": "6ce4b51d-143b-4abf-a392-65818276a216", "name": "User Exists Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1264, -112]}, {"parameters": {"operation": "insert", "tableId": "users", "columns": {"columns": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}, {"keyName": "username", "value": "={{ $json.unifiedUsername || '' }}"}, {"keyName": "name", "value": "={{ (($json.unifiedFirstName || '') + ' ' + ($json.unifiedLastName || '')).trim() || 'User' }}"}, {"keyName": "is_verified_investor", "value": false}, {"keyName": "role", "value": "investor"}]}, "options": {}}, "id": "0ee35794-7902-4156-aaa0-1254bff78d92", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-944, -32], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\n// Debug: Log the user creation result\nconsole.log('New user created:', JSON.stringify($json, null, 2));\n\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true,\n    debug_user_creation: true\n  }\n};"}, "id": "6b5e08ca-cbfd-40aa-a652-a94a9c73eaf4", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-768, -48]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\n// Debug: Log the existing user data\nconsole.log('Existing user found:', JSON.stringify($json, null, 2));\n\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: false,\n    debug_existing_user: true\n  }\n};"}, "id": "5a83ac2c-43f6-4ff2-9260-7018f5c096c7", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-944, -176]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data.split('_')[0] : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "main"}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}]}}, "id": "f862f337-bc69-4bff-a6d7-6026e430c8d5", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-800, 512], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "const user = $json.user;\nconst isAdmin = user?.role === 'admin';\n\nconst welcomeText = $json.is_new_user \n  ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n  : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n\nconst userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n\nconst staticKeyboard = [\n  [\n    { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n    { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n  ],\n  [\n    { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n    { text: \"💝 Donate\", callback_data: \"donate\" }\n  ],\n  [\n    { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n    { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n  ]\n];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: staticKeyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "5e2efed9-fc7d-4b75-9b14-caf85d1de98d", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-384, -160]}, {"parameters": {"chatId": "={{ $json.chatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "71ce4b63-16d4-4dce-a834-23f72a93d466", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-128, -240], "webhookId": "f7808a34-8de2-4cca-9313-c1ec353740df", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "76d13f87-a731-4129-b4ea-f78f3348be30", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-368, 32], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "c1553c59-50b9-4669-beb3-911ca7505f49", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-144, 48]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "17af3c98-5c7a-4491-ae0e-6e2b8ad78abe", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [112, -48], "webhookId": "cf66780a-dbcf-441d-aa1b-e62ffe47170e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "d56b9344-19a9-4f8b-894e-5ee5c6277e0b", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-688, 224], "webhookId": "04b66bdf-bcbc-4144-b12f-6b25d86e4b0c", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}]}}, "id": "95670edb-5fa0-4857-8570-af63829c4bb6", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-464, 272], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true", "rightType": "any"}}, {"leftValue": "={{ $json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "f839a97a-76a6-4c75-be3e-c0a55a82b754", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 560]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Hello {{ $json[0]?.name || 'Investor' }}! Your current investment value is: ${{ $json[0]?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "657ee10c-a560-4dc5-9238-ed4133b40527", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [448, 512], "webhookId": "3fd54c83-3909-4de6-a53c-34c37f734e3b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "7a9395f8-5cab-4c98-bd00-************", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [688, 640], "webhookId": "e797834f-b962-4a70-ad7c-7d45762ad3aa", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst serviceButtons = [\n  [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n  [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n  [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n  [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n];\n\nconst keyboard = [...serviceButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights\\n\\nSelect a service below to proceed:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "fac07698-0467-48f3-8658-fd44e3e7af97", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-448, 688]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "4583f6ec-b19c-450c-b1e2-bdcaf51b19ca", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-192, 688], "webhookId": "ce4c0427-72a9-451e-aa40-4b9dac6bcbf8", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst donateButtons = [\n  [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n  [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n  [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n  [{ text: \"Custom Amount\", callback_data: \"donate_custom\" }]\n];\n\nconst keyboard = [...donateButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "66d2aa60-d37d-4ffd-9736-08c4f32e0bef", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-336, 832]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "46fa94c0-d23e-4ad5-84fd-8d6b0028533f", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-144, 832], "webhookId": "1cca6b01-d29b-4f44-95fc-73fa22064cac", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons.", "additionalFields": {}}, "id": "ab0149d4-8f88-40c0-a4bb-e1d05a88cbb4", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1152, 592], "webhookId": "54a07ea0-e42b-49cd-8ad1-57700d90dc54", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}, {"keyName": "role", "value": "admin"}]}}, "id": "9252337a-bd7b-487f-b1ae-0c70cc5c4bc2", "name": "Check if User is Admin1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-96, 368], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"}, {"keyName": "role", "value": "admin"}]}}, "id": "3f827a76-bea5-42ed-9a86-e694361ba2a1", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-256, 224], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{$node['Check if User is Admin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}, {"leftValue": "={{$node['Check if User is Admin'].json[0]?.role}}", "rightValue": "admin", "operation": {"type": "string", "operation": "equal", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "7ac3c8fe-927d-453c-a3d7-9894bed1424c", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-80, 224]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "b5570bcf-0a21-4022-93b7-520a90147f71", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [288, 160], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $node[\"Check User in Database\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true", "rightType": "any"}}, {"leftValue": "={{ $node[\"Check User in Database\"].json.length }}", "rightValue": 0, "operation": {"type": "number", "operation": "larger", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "942dae9e-94d8-4e30-b514-57bacd4f22b8", "name": "Check if Verified Investor1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [80, 384]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"Check User in Database\"].json[0].name }}! Your current investment value is: ${{ $node[\"Check User in Database\"].json[0].investment_details.total_value_usd }}.", "additionalFields": {}}, "id": "a29a6792-dddb-4db8-9dc6-d1d7ff85152c", "name": "Send Investment Details1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [384, 320], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "❌ *Invalid User Data*\n\nSorry, we couldn't process your request due to invalid user information. This might happen if:\n\n• Your user ID is missing or corrupted\n• There's a temporary data issue\n• The bot needs to be restarted\n\n🔄 Please try using /start to restart the bot.\n📞 If the problem persists, contact support.", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "invalid-user-error", "name": "Invalid User E<PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1744, 448], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "🔧 *Database Connection Error*\n\nWe're experiencing temporary technical difficulties with our database. Please try again in a few moments.\n\n⏳ This is usually resolved quickly\n🔄 Use /start to try again\n📞 Contact support if the issue persists", "parseMode": "<PERSON><PERSON>", "additionalFields": {}}, "id": "database-error-handler", "name": "Database Error <PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1072, 288], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "Standardize User Data": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "debug-before-lookup": {"main": [[{"node": "test-supabase-connection", "type": "main", "index": 0}]]}, "test-supabase-connection": {"main": [[{"node": "Check Existing User", "type": "main", "index": 0}]]}, "Event Router": {"main": [[{"node": "Validate User ID", "type": "main", "index": 0}]]}, "Validate User ID": {"main": [[{"node": "debug-before-lookup", "type": "main", "index": 0}], [{"node": "invalid-user-error", "type": "main", "index": 0}]]}, "Check Existing User": {"main": [[{"node": "debug-user-lookup", "type": "main", "index": 0}]], "error": [[{"node": "database-error-handler", "type": "main", "index": 0}]]}, "debug-user-lookup": {"main": [[{"node": "User Exists Check", "type": "main", "index": 0}]]}, "User Exists Check": {"main": [[{"node": "Create New User", "type": "main", "index": 0}], [{"node": "<PERSON> Existing User", "type": "main", "index": 0}]]}, "Create New User": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]], "error": [[{"node": "Database Error <PERSON>", "type": "main", "index": 0}]]}, "Mark New User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Build Services Menu", "type": "main", "index": 0}], [{"node": "Build Donate Menu", "type": "main", "index": 0}], [{"node": "Unhandled Input", "type": "main", "index": 0}]]}, "Send Welcome": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}]]}, "Build Main Menu": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}]]}, "Mark Existing User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Build Services Menu": {"main": [[{"node": "Send Services Menu", "type": "main", "index": 0}]]}, "Build Donate Menu": {"main": [[{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Check if User is Admin": {"main": [[{"node": "IF Admin", "type": "main", "index": 0}]]}, "IF Admin": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}], [{"node": "Check if Verified Investor1", "type": "main", "index": 0}]]}, "Check if Verified Investor1": {"main": [[{"node": "Send Investment Details1", "type": "main", "index": 0}], [{"node": "Check if Verified Investor", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if User is Admin", "type": "main", "index": 0}]]}, "Send Main Menu": {"main": [[{"node": "Check User in Database", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d7461df8-f263-47d4-8e26-f844588efef8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "yXSgMYBuZvK8mqvY", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}